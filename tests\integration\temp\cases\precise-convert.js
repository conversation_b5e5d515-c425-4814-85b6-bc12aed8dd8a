/**
 * 精确版本的测试用例转换脚本 - 处理复杂的 Excel TSV 格式
 */

const fs = require('fs');

function convertToMarkdown(inputFile, outputFile) {
    console.log('开始精确转换...');
    
    // 读取文件
    const content = fs.readFileSync(inputFile, 'utf-8');
    const lines = content.split(/\r?\n/);
    
    console.log(`读取到 ${lines.length} 行数据`);
    
    // 解析表头
    const headers = lines[0].split('\t');
    console.log('表头:', headers);
    
    // 更精确的解析方法
    const testCases = [];
    let currentCase = null;
    let i = 1;
    
    while (i < lines.length) {
        const line = lines[i].trim();
        if (!line) {
            i++;
            continue;
        }
        
        const parts = line.split('\t');
        
        // 检查是否是新的测试用例（第一列是纯数字）
        if (parts[0] && /^\d+$/.test(parts[0].trim())) {
            // 保存上一个用例
            if (currentCase) {
                testCases.push(currentCase);
            }
            
            // 开始新用例
            currentCase = {
                id: parts[0].trim(),
                testPoint: '',
                testTarget: '',
                testSteps: '',
                verificationItems: ''
            };
            
            // 解析当前行的内容
            if (parts[1]) currentCase.testPoint = parts[1].trim();
            if (parts[2]) currentCase.testTarget = parts[2].trim();
            if (parts[3]) currentCase.testSteps = parts[3].trim();
            if (parts[4]) currentCase.verificationItems = parts[4].trim();
            
            console.log(`解析测试用例 ${currentCase.id}`);
            
            // 继续读取后续行，直到遇到下一个测试用例
            i++;
            while (i < lines.length) {
                const nextLine = lines[i].trim();
                if (!nextLine) {
                    i++;
                    continue;
                }
                
                const nextParts = nextLine.split('\t');
                
                // 如果下一行是新的测试用例，停止
                if (nextParts[0] && /^\d+$/.test(nextParts[0].trim())) {
                    break;
                }
                
                // 否则，将内容添加到当前测试用例的相应字段
                // 根据内容的特征来判断应该添加到哪个字段
                const fullLine = nextLine;
                
                // 如果行以引号开始，可能是跨行内容
                if (fullLine.startsWith('"')) {
                    // 尝试解析为步骤或确认项目
                    if (fullLine.includes('1.') || fullLine.includes('2.') || fullLine.includes('3.')) {
                        if (fullLine.includes('サーバ一覧画面') || fullLine.includes('クリック')) {
                            // 这是试验手順
                            if (currentCase.testSteps) currentCase.testSteps += '\n' + fullLine;
                            else currentCase.testSteps = fullLine;
                        } else if (fullLine.includes('完了') || fullLine.includes('確認')) {
                            // 这是确认项目
                            if (currentCase.verificationItems) currentCase.verificationItems += '\n' + fullLine;
                            else currentCase.verificationItems = fullLine;
                        } else {
                            // 默认添加到试验手順
                            if (currentCase.testSteps) currentCase.testSteps += '\n' + fullLine;
                            else currentCase.testSteps = fullLine;
                        }
                    } else {
                        // 可能是试验观点的续行
                        if (currentCase.testPoint) currentCase.testPoint += '\n' + fullLine;
                        else currentCase.testPoint = fullLine;
                    }
                } else {
                    // 按列解析
                    for (let j = 0; j < Math.min(nextParts.length, 5); j++) {
                        const content = nextParts[j] ? nextParts[j].trim() : '';
                        if (!content) continue;
                        
                        switch (j) {
                            case 1: // 試験観点
                                if (currentCase.testPoint) currentCase.testPoint += '\n' + content;
                                else currentCase.testPoint = content;
                                break;
                            case 2: // 試験対象
                                if (currentCase.testTarget) currentCase.testTarget += '\n' + content;
                                else currentCase.testTarget = content;
                                break;
                            case 3: // 試験手順
                                if (currentCase.testSteps) currentCase.testSteps += '\n' + content;
                                else currentCase.testSteps = content;
                                break;
                            case 4: // 確認項目
                                if (currentCase.verificationItems) currentCase.verificationItems += '\n' + content;
                                else currentCase.verificationItems = content;
                                break;
                        }
                    }
                }
                i++;
            }
        } else {
            i++;
        }
    }
    
    // 添加最后一个用例
    if (currentCase) {
        testCases.push(currentCase);
    }
    
    console.log(`解析完成，共 ${testCases.length} 个测试用例`);
    
    // 生成 Markdown
    let markdown = `# 测试用例文档 (2-4-1)\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    markdown += `## 目录\n\n`;
    
    // 生成目录
    testCases.forEach(testCase => {
        const title = testCase.testPoint.split('\n')[0].replace(/^"/, '').substring(0, 80);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${cleanContent(testCase.testPoint)}\n\n`;
        
        markdown += `### 试验对象\n`;
        if (testCase.testTarget) {
            const targets = testCase.testTarget.split('\n').filter(t => t.trim());
            targets.forEach(target => {
                const cleanTarget = target.replace(/^"/, '').replace(/"$/, '');
                markdown += `- ${cleanTarget}\n`;
            });
        }
        markdown += `\n`;
        
        markdown += `### 试验手順\n`;
        markdown += `${cleanContent(testCase.testSteps)}\n\n`;
        
        markdown += `### 确认项目\n`;
        markdown += `${cleanContent(testCase.verificationItems)}\n\n`;
        
        markdown += `---\n\n`;
    });
    
    // 写入文件
    fs.writeFileSync(outputFile, markdown, 'utf-8');
    
    console.log(`✅ 转换完成！输出文件: ${outputFile}`);
    console.log(`📊 文件大小: ${Math.round(fs.statSync(outputFile).size / 1024)} KB`);
}

function cleanContent(content) {
    if (!content) return '';
    return content
        .replace(/^\n+|\n+$/g, '')
        .replace(/^"+|"+$/g, '')
        .trim();
}

// 运行转换
if (require.main === module) {
    const inputFile = process.argv[2] || '2-4-1.txt';
    const outputFile = process.argv[3] || '2-4-1-precise.md';
    
    convertToMarkdown(inputFile, outputFile);
}
