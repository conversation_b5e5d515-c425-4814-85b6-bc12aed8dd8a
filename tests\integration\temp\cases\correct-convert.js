/**
 * 正确的测试用例转换脚本 - 正确处理带引号的多行列
 */

const fs = require('fs');

function convertToMarkdown(inputFile, outputFile) {
    console.log('开始正确转换...');
    
    // 读取文件
    const content = fs.readFileSync(inputFile, 'utf-8');
    const lines = content.split(/\r?\n/);
    
    console.log(`读取到 ${lines.length} 行数据`);
    
    // 解析表头
    const headers = lines[0].split('\t');
    console.log('表头:', headers);
    
    // 正确解析 TSV 格式，处理带引号的多行字段
    const testCases = [];
    let i = 1;
    
    while (i < lines.length) {
        const line = lines[i].trim();
        if (!line) {
            i++;
            continue;
        }
        
        // 解析一行，可能包含多行的引号字段
        const fields = parseTSVLine(lines, i);
        if (!fields || fields.length < 5) {
            i++;
            continue;
        }
        
        // 检查是否是新的测试用例（第一列是数字）
        if (/^\d+$/.test(fields[0].trim())) {
            const testCase = {
                id: fields[0].trim(),
                testPoint: fields[1].trim(),
                testTarget: fields[2].trim(),
                testSteps: fields[3].trim(),
                verificationItems: fields[4].trim()
            };
            
            testCases.push(testCase);
            console.log(`解析测试用例 ${testCase.id}: ${testCase.testPoint.substring(0, 50)}...`);
        }
        
        // 跳过已处理的行
        i = fields.nextLineIndex || i + 1;
    }
    
    console.log(`解析完成，共 ${testCases.length} 个测试用例`);
    
    // 生成 Markdown
    let markdown = `# 测试用例文档 (2-4-1)\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    markdown += `## 目录\n\n`;
    
    // 生成目录
    testCases.forEach(testCase => {
        const title = testCase.testPoint.substring(0, 80);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${testCase.testPoint}\n\n`;
        
        markdown += `### 试验对象\n`;
        if (testCase.testTarget) {
            const targets = testCase.testTarget.split('\n').filter(t => t.trim());
            targets.forEach(target => {
                markdown += `- ${target.trim()}\n`;
            });
        }
        markdown += `\n`;
        
        markdown += `### 试验手順\n`;
        if (testCase.testSteps) {
            const steps = testCase.testSteps.split('\n').filter(s => s.trim());
            steps.forEach((step, index) => {
                markdown += `${step.trim()}\n\n`;
            });
        }
        
        markdown += `### 确认项目\n`;
        if (testCase.verificationItems) {
            const items = testCase.verificationItems.split('\n').filter(item => item.trim());
            items.forEach((item, index) => {
                markdown += `${item.trim()}\n\n`;
            });
        }
        
        markdown += `---\n\n`;
    });
    
    // 写入文件
    fs.writeFileSync(outputFile, markdown, 'utf-8');
    
    console.log(`✅ 转换完成！输出文件: ${outputFile}`);
    console.log(`📊 文件大小: ${Math.round(fs.statSync(outputFile).size / 1024)} KB`);
}

/**
 * 解析 TSV 行，正确处理带引号的多行字段
 * 简化版本：直接处理整个文件内容
 */
function parseTSVLine(lines, startIndex) {
    // 找到完整的记录
    let currentIndex = startIndex;
    let fullRecord = '';

    // 从当前行开始，找到下一个以数字开头的行（下一个测试用例）
    while (currentIndex < lines.length) {
        const line = lines[currentIndex];

        // 如果是下一个测试用例的开始（以数字+tab开头），停止
        if (currentIndex > startIndex && /^\d+\t/.test(line)) {
            break;
        }

        if (fullRecord) {
            fullRecord += '\n' + line;
        } else {
            fullRecord = line;
        }
        currentIndex++;
    }

    // 现在解析完整的记录
    const fields = parseCompleteRecord(fullRecord);

    return { ...fields, nextLineIndex: currentIndex };
}

/**
 * 解析完整的测试用例记录
 */
function parseCompleteRecord(record) {
    // 使用正则表达式来正确分割字段
    const fields = [];

    // 第一个字段：数字ID
    const idMatch = record.match(/^(\d+)\t/);
    if (!idMatch) return null;

    fields[0] = idMatch[1];
    let remaining = record.substring(idMatch[0].length);

    // 第二个字段：试验观点（到第一个引号前的tab）
    const pointMatch = remaining.match(/^([^"]*?)\t"/);
    if (!pointMatch) return null;

    fields[1] = pointMatch[1].trim();
    remaining = remaining.substring(pointMatch[0].length - 1); // 保留引号

    // 解析剩余的引号字段
    let fieldIndex = 2;
    while (remaining && fieldIndex < 5) {
        if (remaining.startsWith('"')) {
            // 找到匹配的结束引号
            let quoteEnd = 1;
            let inQuote = true;

            while (quoteEnd < remaining.length && inQuote) {
                if (remaining[quoteEnd] === '"') {
                    // 检查是否是真正的结束引号（后面跟tab或结束）
                    if (quoteEnd === remaining.length - 1 || remaining[quoteEnd + 1] === '\t') {
                        inQuote = false;
                    }
                }
                if (inQuote) quoteEnd++;
            }

            if (!inQuote) {
                fields[fieldIndex] = remaining.substring(1, quoteEnd);
                remaining = remaining.substring(quoteEnd + 1);
                if (remaining.startsWith('\t')) {
                    remaining = remaining.substring(1);
                }
                fieldIndex++;
            } else {
                break;
            }
        } else {
            break;
        }
    }

    return fields;
}

// 运行转换
if (require.main === module) {
    const inputFile = process.argv[2] || '2-4-1.txt';
    const outputFile = process.argv[3] || '2-4-1-correct.md';
    
    convertToMarkdown(inputFile, outputFile);
}
