# 测试用例文档 (2-4-1)

> 从 Excel 文件转换生成，共 298 个测试用例

## 目录

- [1. 開始日と終了日を同じ日に指定し、出力期間が1日の場合に操作ログのエクスポートタスクが正常終了する](#测试用例-1)
- [2. 出力期間が7日、かつ開始日と終了日が同じ月の場合に操作ログのエクスポートタスクが正常終了する。](#测试用例-2)
- [3. 出力期間が29日、かつ開始日と終了日が月を跨ぐ場合に操作ログのエクスポートタスクが正常終了する。](#测试用例-3)
- [4. 出力期間がデフォルトの最大期間30日で、複数件の操作ログファイルが出力された場合、操作ログのエクスポートタスクが正常終了する](#测试用例-4)
- [5. ハードウェア資産情報の追加管理項目「テスト項目」に「ああああ」を追加するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。](#测试用例-5)
- [6. ハードウェア資産情報の追加管理項目「テスト項目」の「ああああ」を「いいいい」に変更するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。](#测试用例-6)
- [7. ハードウェア資産情報の追加管理項目「テスト項目」の「いいいい」を削除するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。](#测试用例-7)
- [8. ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加し、管理項目定義のエクスポートタスクが正常終了する。](#测试用例-8)
- [9. 複数のユーザーが同時に同じサーバに対してタスクを実行する場合](#测试用例-9)
- [10. Runbookジョブの実行時間（タスクの開始日時から経過した時間）が5時間を超えた場合タスクがタイムアウトしたと検知され、エラーに更新される](#测试用例-10)
- [11. タスク受け付け処理：操作ログのエクスポートタスクで、受け付け成功の場合のメッセージ](#测试用例-11)
- [12. タスク受け付け処理：管理項目定義のインポートタスクで、受け付け成功の場合のメッセージ](#测试用例-12)
- [13. タスク受け付け処理：管理項目定義のエクスポートタスクで、受け付け成功の場合のメッセージ](#测试用例-13)
- [14. タスク受け付け処理：管理項目定義のインポートタスクにおいて、メモ帳で編集・保存したCSVファイルをインポートできることの確認](#测试用例-14)
- [15. タスク受け付け処理：管理項目定義のインポートタスクにおいて、EXCELで編集・保存したCSVファイルをインポートできることの確認](#测试用例-15)
- [16. タスク受け付け処理：管理項目定義のインポートタスクにおいて、10MBまでのCSVファイルをインポートできることの確認](#测试用例-16)
- [17. タスク受け付け処理：操作ログのエクスポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット](#测试用例-17)
- [18. タスク受け付け処理：管理項目定義のインポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット](#测试用例-18)
- [19. タスク受け付け処理：管理項目定義のエクスポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット](#测试用例-19)
- [20. タスク受け付け処理：受け付け成功の場合に作成されたタスク名に入っている日時のタイムゾーン](#测试用例-20)
- [21. タスク受け付け処理：受け付け成功の場合に作成されたタスク名に入っているサーバ名](#测试用例-21)
- [22. タスク受け付け処理：受け付け成功の場合に作成されたタスクのステータスとタスク詳細](#测试用例-22)
- [23. タスク受け付け処理：ログインユーザーのセッションが期限切れ](#测试用例-23)
- [24. タスク受け付け処理：入力パラメータのタスク種別が存在しない](#测试用例-24)
- [25. タスク受け付け処理：入力パラメータのタスク種別がnull](#测试用例-25)
- [26. タスク受け付け処理：入力パラメータのタスク種別が空の文字列](#测试用例-26)
- [27. タスク受け付け処理：入力パラメータの対象サーバIDが存在しない](#测试用例-27)
- [28. タスク受け付け処理：入力パラメータの対象サーバIDがnull](#测试用例-28)
- [29. タスク受け付け処理：入力パラメータの対象サーバIDが空の文字列](#测试用例-29)
- [30. タスク受け付け処理：入力パラメータのタスク種別が操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートのどれでもない](#测试用例-30)
- [31. タスク受け付け処理：サーバ情報取得時DB読み取り失敗](#测试用例-31)
- [32. タスク受け付け処理：サーバ情報取得時レコードが存在しない](#测试用例-32)
- [33. タスク受け付け処理：サーバ情報のAzure VM名がnull](#测试用例-33)
- [34. タスク受け付け処理：サーバ情報のAzure VM名が空の文字列](#测试用例-34)
- [35. タスク受け付け処理：サーバ情報のDockerコンテナ名がnull](#测试用例-35)
- [36. タスク受け付け処理：サーバ情報のDockerコンテナ名が空の文字列](#测试用例-36)
- [37. タスク受け付け処理：サーバ情報のHRWグループ名がnull](#测试用例-37)
- [38. タスク受け付け処理：サーバ情報のHRWグループ名が空の文字列](#测试用例-38)
- [39. タスク受け付け処理：コンテナ実行状態取得時DB読み取り失敗](#测试用例-39)
- [40. タスク受け付け処理：コンテナ実行状態のレコードが存在する、かつステータスがBUSY](#测试用例-40)
- [41. タスク受け付け処理：コンテナ実行状態のレコードが存在する、かつステータスがBUSYまたはIDLE以外の値](#测试用例-41)
- [42. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が存在しない](#测试用例-42)
- [43. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日がnull](#测试用例-43)
- [44. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が空の文字列](#测试用例-44)
- [45. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日がYYYY-MM-DDのフォーマットではない](#测试用例-45)
- [46. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が実際に存在しない日付](#测试用例-46)
- [47. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が存在しない](#测试用例-47)
- [48. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日がnull](#测试用例-48)
- [49. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が空の文字列](#测试用例-49)
- [50. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日がYYYY-MM-DDのフォーマットではない](#测试用例-50)
- [51. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が実際に存在しない日付](#测试用例-51)
- [52. タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が開始日より前の日付](#测试用例-52)
- [53. タスク受け付け処理：値の一覧テーブルにOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）の](#测试用例-53)
- [54. タスク受け付け処理：値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）の](#测试用例-54)
- [55. タスク受け付け処理：操作ログのエクスポート最大期間がデフォルトの30日。指定された期間が30日のタスクを作成](#测试用例-55)
- [56. タスク受け付け処理：操作ログのエクスポート最大期間がデフォルトの30日。指定された期間がデフォルトの最大期間30日を超えた](#测试用例-56)
- [57. タスク受け付け処理：操作ログのエクスポート最大期間が7日。指定された期間が7日のタスクを作成](#测试用例-57)
- [58. タスク受け付け処理：操作ログのエクスポート最大期間が7日。指定された期間が最大期間7日を超えた](#测试用例-58)
- [59. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルが存在しない](#测试用例-59)
- [60. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルがnull](#测试用例-60)
- [61. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのサイズが0](#测试用例-61)
- [62. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名が存在しない](#测试用例-62)
- [63. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名がnull](#测试用例-63)
- [64. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名が空の文字列](#测试用例-64)
- [65. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルの拡張子がcsvでない](#测试用例-65)
- [66. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのMIMEタイプがcsvでない](#测试用例-66)
- [67. タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのサイズが制限の10MBを超えた](#测试用例-67)
- [68. タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFの値が不正](#测试用例-68)
- [69. タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGが存在しない](#测试用例-69)
- [70. タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値がnull](#测试用例-70)
- [71. タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値が空の文字列](#测试用例-71)
- [72. タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値が不正](#测试用例-72)
- [73. タスク受け付け処理：ファイルのBlobへのアップロードに失敗](#测试用例-73)
- [74. タスク受け付け処理：接続不可によってコンテナ実行状態テーブルへの新規レコード作成に失敗](#测试用例-74)
- [75. タスク受け付け処理：コンテナ実行状態テーブルに該当のレコードが存在しない場合、同じサーバに対して複数のユーザーが同時にタスクを作成し、一番先のタスク受け付け処理](#测试用例-75)
- [76. タスク受け付け処理：タスクテーブルへの新規レコード作成に失敗](#测试用例-76)
- [77. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数SERVICE_BUS_TASK_INPUT_QU](#测试用例-77)
- [78. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPAC](#测试用例-78)
- [79. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPAC](#测试用例-79)
- [80. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPAC](#测试用例-80)
- [81. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPAC](#测试用例-81)
- [82. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージの送信に失敗](#测试用例-82)
- [83. タスク受け付け処理：未分類のエラーが発生](#测试用例-83)
- [84. タスク実行関数：タスク実行関数が処理成功の場合、タスク情報の更新](#测试用例-84)
- [85. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が10件。古いタスク記録のク](#测试用例-85)
- [86. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-86)
- [87. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-87)
- [88. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-88)
- [89. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、今実行中のタスク](#测试用例-89)
- [90. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、すべて「正常終了](#测试用例-90)
- [91. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、すべて「正常終了](#测试用例-91)
- [92. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、その内今実行中の](#测试用例-92)
- [93. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-93)
- [94. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-94)
- [95. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が13件で、今実行中のタスク](#测试用例-95)
- [96. タスク実行関数：サーバごとのタスク記録の最大保持件数が5件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が5件。古いタスク記録のクリーンアップ処理](#测试用例-96)
- [97. タスク実行関数：サーバごとのタスク記録の最大保持件数が5件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が6件で、今実行中のタスクを除いてすべて「](#测试用例-97)
- [98. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。サーバAが5件、サーバBが6件のタスク記録を持っていて、すべてが「正常終了」のステータ](#测试用例-98)
- [99. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。サーバAが10件、サーバBが6件のタスク記録を持っていて、すべてが「正常終了」のステー](#测试用例-99)
- [100. タスク実行関数：TaskInputQueueから受信したメッセージがnull](#测试用例-100)
- [101. タスク実行関数：TaskInputQueueから受信したメッセージがundefined](#测试用例-101)
- [102. タスク実行関数：TaskInputQueueから受信したメッセージが空の文字列](#测试用例-102)
- [103. タスク実行関数：TaskInputQueueから受信したメッセージがobject型ではない](#测试用例-103)
- [104. タスク実行関数：TaskInputQueueから受信したタスクIDがnull](#测试用例-104)
- [105. タスク実行関数：TaskInputQueueから受信したタスクIDがundefined](#测试用例-105)
- [106. タスク実行関数：TaskInputQueueから受信したタスクIDが空の文字列](#测试用例-106)
- [107. タスク実行関数：TaskInputQueueから受信したタスクIDが文字列型ではない](#测试用例-107)
- [108. タスク実行関数：TaskInputQueueから受信したタスクIDがタスクテーブルに存在しない文字列](#测试用例-108)
- [109. タスク実行関数：タスクテーブルからタスク情報取得時、DB読み取り失敗](#测试用例-109)
- [110. タスク実行関数：タスクテーブルからタスク情報取得時、タスク種別が空の文字列](#测试用例-110)
- [111. タスク実行関数：タスクテーブルからタスク情報取得時、サーバIDが空の文字列](#测试用例-111)
- [112. タスク実行関数：タスクテーブルからタスク情報取得時、サーバ名がnull](#测试用例-112)
- [113. タスク実行関数：タスクテーブルからタスク情報取得時、サーバ名が空の文字列](#测试用例-113)
- [114. タスク実行関数：タスクテーブルからタスク情報取得時、VM名がnull](#测试用例-114)
- [115. タスク実行関数：タスクテーブルからタスク情報取得時、VM名が空の文字列](#测试用例-115)
- [116. タスク実行関数：タスクテーブルからタスク情報取得時、コンテナ名がnull](#测试用例-116)
- [117. タスク実行関数：タスクテーブルからタスク情報取得時、コンテナ名が空の文字列](#测试用例-117)
- [118. タスク実行関数：タスクテーブルからタスク情報取得時、HRWグループ名がnull](#测试用例-118)
- [119. タスク実行関数：タスクテーブルからタスク情報取得時、HRWグループ名が空の文字列](#测试用例-119)
- [120. タスク実行関数：タスクテーブルからタスク情報取得時、タスクのステータスがQUEUED以外（ユーザーが先に中止操作を行った）](#测试用例-120)
- [121. タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、DB読み取り失敗](#测试用例-121)
- [122. タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、レコードが存在しない](#测试用例-122)
- [123. タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、ステータスが空の文字列](#测试用例-123)
- [124. タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、ステータスがBUSY（コンテナ実行状態テーブルに該当のレコードが存在している場合、同じ](#测试用例-124)
- [125. タスク実行関数：コンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新する時、更新した結果が0件（コンテナ実行状態テーブルに該当のレコードが存在](#测试用例-125)
- [126. タスク実行関数：コンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新する時、DB書き込み失敗](#测试用例-126)
- [127. タスク実行関数：Azure Filesの作業ディレクトリtaskworkspacesの作成に失敗](#测试用例-127)
- [128. タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/の作成に失敗](#测试用例-128)
- [129. タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/imports/の作成に失敗](#测试用例-129)
- [130. タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/exports/の作成に失敗](#测试用例-130)
- [131. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメ](#测试用例-131)
- [132. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメ](#测试用例-132)
- [133. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメ](#测试用例-133)
- [134. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメ](#测试用例-134)
- [135. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZ](#测试用例-135)
- [136. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZ](#测试用例-136)
- [137. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZ](#测试用例-137)
- [138. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZ](#测试用例-138)
- [139. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、Azure ](#测试用例-139)
- [140. タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、Azure ](#测试用例-140)
- [141. タスク実行関数：タスクテーブルから該当のレコードのステータスをRUNBOOK_SUBMITTEDに更新する時、DB書き込み失敗](#测试用例-141)
- [142. タスク実行関数：タスクテーブルから該当のレコードのステータスをRUNBOOK_SUBMITTEDに更新する時、更新した結果が0件（ユーザーが先に中止操作を行った](#测试用例-142)
- [143. タスク実行関数：タスクテーブルから取得したタスク情報のタスク種別が操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートのどれでもない](#测试用例-143)
- [144. タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが存在しない](#测试用例-144)
- [145. タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTがnull](#测试用例-145)
- [146. タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが空の文字列](#测试用例-146)
- [147. タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが実際に存在しないRunbook名](#测试用例-147)
- [148. タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが存在しない](#测试用例-148)
- [149. タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTがnull](#测试用例-149)
- [150. タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが空の文字列](#测试用例-150)
- [151. タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが実際に存在しないRunbook名](#测试用例-151)
- [152. タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが存在しない](#测试用例-152)
- [153. タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTがnull](#测试用例-153)
- [154. タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが空の文字列](#测试用例-154)
- [155. タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが実際に存在しないRunbook名](#测试用例-155)
- [156. タスク実行関数：タスクテーブルから取得したタスク情報のHRWグループ名が実際に存在しないグループ名](#测试用例-156)
- [157. タスク実行関数：Azure AutomationのRunbookジョブ作成APIの呼び出しに失敗](#测试用例-157)
- [158. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-158)
- [159. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-159)
- [160. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-160)
- [161. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-161)
- [162. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-162)
- [163. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-163)
- [164. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-164)
- [165. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-165)
- [166. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-166)
- [167. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-167)
- [168. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-168)
- [169. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-169)
- [170. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-170)
- [171. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-171)
- [172. タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスク](#测试用例-172)
- [173. タスク実行関数：処理中に未分類のエラーが発生](#测试用例-173)
- [174. "タスク実行関数：タイムアウト時間が5分の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。](#测试用例-174)
- [175. "タスク実行関数：エラー発生時の補償処理のDB更新に失敗の場合の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。](#测试用例-175)
- [176. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがnull](#测试用例-176)
- [177. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがundefined](#测试用例-177)
- [178. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージが空の文字列](#测试用例-178)
- [179. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがobject型ではない](#测试用例-179)
- [180. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがnull](#测试用例-180)
- [181. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがundefined](#测试用例-181)
- [182. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDが空の文字列](#测试用例-182)
- [183. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDが文字列型ではない](#测试用例-183)
- [184. タスク実行タイムアウト関数：Azure Automationのジョブ取得APIの呼び出しに失敗](#测试用例-184)
- [185. タスク実行タイムアウト関数：Runbookジョブ作成APIが呼び出された後タスク実行関数の実行時間が5分を超えてタイムアウトする場合、タスク実行タイムアウト関数](#测试用例-185)
- [186. タスク実行タイムアウト関数：Azure Files上のタスク作業ディレクトリ（taskworkspaces/<taskId>/）の削除に失敗](#测试用例-186)
- [187. タスク実行タイムアウト関数：タスクテーブルからタスク情報を取得する時DB読み取り失敗](#测试用例-187)
- [188. タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがタスクテーブルに存在しない](#测试用例-188)
- [189. タスク実行タイムアウト関数：コンテナ実行状態テーブルの更新に失敗](#测试用例-189)
- [190. タスク実行タイムアウト関数：コンテナ実行状態テーブルの更新結果が0件（該当コンテナのステータスがIDLEのためタスク実行タイムアウト関数の更新条件と一致しない）](#测试用例-190)
- [191. タスク実行タイムアウト関数：タスクのステータスがPENDING_CANCELLATIONまたはCANCELLED](#测试用例-191)
- [192. タスク実行タイムアウト関数：タスクテーブルの更新に失敗](#测试用例-192)
- [193. タスク実行タイムアウト関数：タスクテーブルの更新結果が0件](#测试用例-193)
- [194. タスク実行タイムアウト関数：予期せぬ内部エラーが発生](#测试用例-194)
- [195. タスク実行タイムアウト関数：タイムアウト時間が5分の確認。及び関数が異常終了した後の仕様確認。](#测试用例-195)
- [196. Runbookジョブ監視関数：環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）がデフォルトの30秒](#测试用例-196)
- [197. Runbookジョブ監視関数：環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）が60秒](#测试用例-197)
- [198. Runbookジョブ監視関数：実行中のRunbookジョブ監視関数がある場合新たに起動されない（Runbookジョブ監視関数の複数同時実行が発生しない）](#测试用例-198)
- [199. Runbookジョブ監視関数：環境変数AZURE_AUTOMATION_ACCOUNT_NAMEがnull/undefined/空の文字列](#测试用例-199)
- [200. Runbookジョブ監視関数：環境変数SUBSCRIPTION_IDがnull/undefined/空の文字列](#测试用例-200)
- [201. Runbookジョブ監視関数：環境変数RESOURCE_GROUP_NAMEがnull/undefined/空の文字列](#测试用例-201)
- [202. Runbookジョブ監視関数：環境変数SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAMEがnull/undefined/空の文字列](#测试用例-202)
- [203. Runbookジョブ監視関数：タスクテーブルからRUNBOOK_SUBMITTEDステータスのレコードを取得する時DB読み取り失敗](#测试用例-203)
- [204. Runbookジョブ監視関数：タスクテーブルからRUNBOOK_SUBMITTEDステータスのレコードを取得した結果が0件](#测试用例-204)
- [205. Runbookジョブ監視関数：タスクテーブルから取得したタスクの開始日時がnull](#测试用例-205)
- [206. Runbookジョブ監視関数：環境変数RUNBOOK_TIMEOUT_SECONDS（Runbookジョブの最大実行許容時間）が1時間。タスクの開始日時から経過](#测试用例-206)
- [207. Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIの呼び出しに失敗](#测试用例-207)
- [208. Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIの取得結果はジョブが存在しない](#测试用例-208)
- [209. Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIで取得したジョブのステータスがN](#测试用例-209)
- [210. Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、タスクテーブルからステータスをRUNBOOK_PROCESSING](#测试用例-210)
- [211. Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、タスクテーブルからステータスをRUNBOOK_PROCESSING](#测试用例-211)
- [212. Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タ](#测试用例-212)
- [213. Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タ](#测试用例-213)
- [214. Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タ](#测试用例-214)
- [215. Runbookジョブ監視関数：タイムアウト時間が5分の確認](#测试用例-215)
- [216. Runbookジョブ監視関数：RUNBOOK_SUBMITTEDステータスのタスクを処理する時に予期せぬ内部エラーが発生](#测试用例-216)
- [217. Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがnull](#测试用例-217)
- [218. Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがundefined](#测试用例-218)
- [219. Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージが空の文字列](#测试用例-219)
- [220. Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがobject型ではない](#测试用例-220)
- [221. Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDがnull](#测试用例-221)
- [222. Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDがundefined](#测试用例-222)
- [223. Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDが空の文字列](#测试用例-223)
- [224. Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDが文字列型ではない](#测试用例-224)
- [225. Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがnull](#测试用例-225)
- [226. Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがundefined](#测试用例-226)
- [227. Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスが空の文字列](#测试用例-227)
- [228. Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスが文字列型ではない](#测试用例-228)
- [229. Runbookジョブ処理関数：タスクテーブルからタスク情報を取得する時DB読み取り失敗](#测试用例-229)
- [230. Runbookジョブ処理関数：タスクテーブルからタスク情報を取得した結果は該当のタスクレコードが存在しない](#测试用例-230)
- [231. Runbookジョブ処理関数：対象タスクのステータスがRUNBOOK_PROCESSINGではない](#测试用例-231)
- [232. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例外が](#测试用例-232)
- [233. Runbookジョブ処理関数：管理項目定義のインポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例外](#测试用例-233)
- [234. Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例](#测试用例-234)
- [235. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspa](#测试用例-235)
- [236. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspa](#测试用例-236)
- [237. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspa](#测试用例-237)
- [238. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログファイルのコピーに失敗（Azu](#测试用例-238)
- [239. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログファイルのコピーに失敗（Azu](#测试用例-239)
- [240. Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、目標ファイルtaskworkspa](#测试用例-240)
- [241. Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、管理項目定義ファイルのコピーに失敗](#测试用例-241)
- [242. Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、管理項目定義ファイルのコピーに失敗](#测试用例-242)
- [243. Runbookジョブ処理関数：RunbookジョブのステータスがCompletedで、タスク種別が操作ログのエクスポート、管理項目定義のエクスポート、管理項目定](#测试用例-243)
- [244. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在](#测试用例-244)
- [245. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txtが存在しない場合](#测试用例-245)
- [246. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在](#测试用例-246)
- [247. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空である）が存在し](#测试用例-247)
- [248. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードSJIS、内容が空ではない）が存在し](#测试用例-248)
- [249. Runbookジョブ処理関数：RunbookジョブのステータスがRemoving / Stopped / Stoppingのいずれか](#测试用例-249)
- [250. Runbookジョブ処理関数：RunbookジョブのステータスがResuming / Suspended / Suspendingのいずれかの時、Runbook](#测试用例-250)
- [251. Runbookジョブ処理関数：RunbookジョブのステータスがResuming / Suspended / Suspendingのいずれかの時、Runbook](#测试用例-251)
- [252. Runbookジョブ処理関数：RunbookジョブのステータスがTimeoutの時、RunbookジョブストップAPI呼び出し失敗](#测试用例-252)
- [253. Runbookジョブ処理関数：RunbookジョブのステータスがTimeoutの時、RunbookジョブストップAPI呼び出し成功](#测试用例-253)
- [254. Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがCompleted / Failed / Removing / ](#测试用例-254)
- [255. Runbookジョブ処理関数：Azure Files上の作業ディレクトリtaskworkspaces/<taskId>/の削除に失敗](#测试用例-255)
- [256. Runbookジョブ処理関数：Azure Files上の作業ディレクトリtaskworkspaces/<taskId>/の削除に成功](#测试用例-256)
- [257. Runbookジョブ処理関数：タスクテーブルの更新時にDB書き込み失敗](#测试用例-257)
- [258. Runbookジョブ処理関数：タスクテーブルの更新結果が0件](#测试用例-258)
- [259. Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログテーブルへのレコード作成に失敗](#测试用例-259)
- [260. Runbookジョブ処理関数：コンテナ実行状態テーブルの更新時にDB書き込み失敗](#测试用例-260)
- [261. Runbookジョブ処理関数：コンテナ実行状態テーブルの更新結果が0件](#测试用例-261)
- [262. Runbookジョブ処理関数：タイムアウト時間が30分の確認](#测试用例-262)
- [263. "Runbookジョブ処理関数：リトライ回数が2（RunbookStatusQueueの最大配信数が3）の確認](#测试用例-263)
- [264. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがnull](#测试用例-264)
- [265. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがundefined](#测试用例-265)
- [266. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージが空の文字列](#测试用例-266)
- [267. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがobject型ではない](#测试用例-267)
- [268. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDがnull](#测试用例-268)
- [269. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDがundefined](#测试用例-269)
- [270. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDが空の文字列](#测试用例-270)
- [271. Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDが文字列型ではない](#测试用例-271)
- [272. Runbookジョブ処理タイムアウト関数：タスクテーブルからタスク情報を取得する時レコードが存在しない](#测试用例-272)
- [273. Runbookジョブ処理タイムアウト関数：タスクステータスがRUNBOOK_PROCESSINGではない](#测试用例-273)
- [274. Runbookジョブ処理タイムアウト関数：コンテナ実行状態テーブル更新時DB書き込み失敗](#测试用例-274)
- [275. Runbookジョブ処理タイムアウト関数：コンテナ実行状態テーブル更新時更新結果が0件](#测试用例-275)
- [276. Runbookジョブ処理タイムアウト関数：タスクテーブル更新時DB書き込み失敗](#测试用例-276)
- [277. Runbookジョブ処理タイムアウト関数：タスクテーブル更新時更新結果が0件](#测试用例-277)
- [278. Runbookジョブ処理タイムアウト関数：Azure Files上の作業ディレクトリの削除に失敗](#测试用例-278)
- [279. Runbookジョブ処理タイムアウト関数：タイムアウト時間が5分の確認、及び関数が異常終了した後の仕様確認](#测试用例-279)
- [280. Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で実行時間が30分を超えてタイムアウトする場合、Runbookジョブ処理タイ](#测试用例-280)
- [281. Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で例外が発生する場合、Runbookジョブ処理タイムアウト関数が起動しDBか](#测试用例-281)
- [282. 複数ライセンスのユーザーが同時にタスクを実行する場合](#测试用例-282)
- [283. 開始日と終了日を同じ日に指定し、出力期間が1日の場合に操作ログのエクスポートタスクが正常終了する（中継マネージャ）](#测试用例-283)
- [284. 出力期間が7日、かつ開始日と終了日が同じ月の場合に操作ログのエクスポートタスクが正常終了する。（中継マネージャ）](#测试用例-284)
- [285. 出力期間が29日、かつ開始日と終了日が月を跨ぐ場合に操作ログのエクスポートタスクが正常終了する。（中継マネージャ）](#测试用例-285)
- [286. 出力期間がデフォルトの最大期間30日で、複数件の操作ログファイルが出力された場合、操作ログのエクスポートタスクが正常終了する（中継マネージャ）](#测试用例-286)
- [287. ハードウェア資産情報の追加管理項目「テスト項目」に「ああああ」を追加するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中継マネージャ）](#测试用例-287)
- [288. ハードウェア資産情報の追加管理項目「テスト項目」の「ああああ」を「いいいい」に変更するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中](#测试用例-288)
- [289. ハードウェア資産情報の追加管理項目「テスト項目」の「いいいい」を削除するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中継マネージャ）](#测试用例-289)
- [290. ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加し、管理項目定義のエクスポートタスクが正常終了する。（中継マネージャ）](#测试用例-290)
- [291. 複数のユーザーが同時に同じサーバに対してタスクを実行する場合（中継マネージャ）](#测试用例-291)
- [292. Runbookジョブの実行時間（タスクの開始日時から経過した時間）が5時間を超えた場合タスクがタイムアウトしたと検知され、エラーに更新される（中継マネージャ）](#测试用例-292)
- [293. タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージの送信に失敗（中継マネージャ）](#测试用例-293)
- [294. タスク実行関数：Azure AutomationのRunbookジョブ作成APIの呼び出しに失敗（中継マネージャ）](#测试用例-294)
- [295. "タスク実行関数：タイムアウト時間が5分の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。](#测试用例-295)
- [296. Runbookジョブ監視関数：タイムアウト時間が5分の確認（中継マネージャ）](#测试用例-296)
- [297. Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在](#测试用例-297)
- [298. Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で実行時間が30分を超えてタイムアウトする場合、Runbookジョブ処理タイ](#测试用例-298)

---

## 测试用例 1

### 试验观点
開始日と終了日を同じ日に指定し、出力期間が1日の場合に操作ログのエクスポートタスクが正常終了する
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 2

### 试验观点
出力期間が7日、かつ開始日と終了日が同じ月の場合に操作ログのエクスポートタスクが正常終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 3

### 试验观点
出力期間が29日、かつ開始日と終了日が月を跨ぐ場合に操作ログのエクスポートタスクが正常終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 4

### 试验观点
出力期間がデフォルトの最大期間30日で、複数件の操作ログファイルが出力された場合、操作ログのエクスポートタスクが正常終了する
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 5

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」に「ああああ」を追加するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 6

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」の「ああああ」を「いいいい」に変更するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 7

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」の「いいいい」を削除するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 8

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加し、管理項目定義のエクスポートタスクが正常終了する。
"1. 資産管理項目の設定画面で、ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加する。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 9

### 试验观点
複数のユーザーが同時に同じサーバに対してタスクを実行する場合
"3人のユーザーが同時に同じサーバに対して下記の操作を行う。
"1. 一人だけタスクの実行に成功し、タスクのステータスが「正常終了」になる。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 10

### 试验观点
Runbookジョブの実行時間（タスクの開始日時から経過した時間）が5時間を超えた場合タスクがタイムアウトしたと検知され、エラーに更新される
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。環境変数RUNBOOK_TIMEOUT_SECONDSがデフォルトの18000。
"1. 作成されたタスクのステータスが「実行中」から「エラー」に変わり、終了日時が開始日時から5時間経過した時刻に更新され、タスク詳細が空白から「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 11

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクで、受け付け成功の場合のメッセージ
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 12

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクで、受け付け成功の場合のメッセージ
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 13

### 试验观点
タスク受け付け処理：管理項目定義のエクスポートタスクで、受け付け成功の場合のメッセージ
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 14

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクにおいて、メモ帳で編集・保存したCSVファイルをインポートできることの確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
1. メモ帳で編集・保存したCSVファイルをインポートできること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 15

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクにおいて、EXCELで編集・保存したCSVファイルをインポートできることの確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
1. EXCELで編集・保存したCSVファイルをインポートできること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 16

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクにおいて、10MBまでのCSVファイルをインポートできることの確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
1. 10MBまでのCSVファイルをインポートできること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 17

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
1. 作成されたタスクの名前が　{対象サーバ名}-操作ログのエクスポート-{作成された日時}　であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 18

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
1. 作成されたタスクの名前が　{対象サーバ名}-管理項目定義のインポート-{作成された日時}　であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 19

### 试验观点
タスク受け付け処理：管理項目定義のエクスポートタスクで、受け付け成功の場合に作成されたタスク名のフォーマット
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
1. 作成されたタスクの名前が　{対象サーバ名}-管理項目定義のエクスポート-{作成された日時}　であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 20

### 试验观点
タスク受け付け処理：受け付け成功の場合に作成されたタスク名に入っている日時のタイムゾーン
"1. クライアントユーザーのOSのタイムゾーンを日本（UTC+09:00）に設定する。
"1. 1件目のタスクの名前に入っている日時が作成された時の日本タイムゾーン（UTC+09:00）の日時であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 21

### 试验观点
タスク受け付け処理：受け付け成功の場合に作成されたタスク名に入っているサーバ名
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成された2件のタスクの名前に入っているサーバ名がそれぞれの対象サーバと一致すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 22

### 试验观点
タスク受け付け処理：受け付け成功の場合に作成されたタスクのステータスとタスク詳細
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 23

### 试验观点
タスク受け付け処理：ログインユーザーのセッションが期限切れ
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. ログイン画面に遷移すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 24

### 试验观点
タスク受け付け処理：入力パラメータのタスク種別が存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 25

### 试验观点
タスク受け付け処理：入力パラメータのタスク種別がnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 26

### 试验观点
タスク受け付け処理：入力パラメータのタスク種別が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 27

### 试验观点
タスク受け付け処理：入力パラメータの対象サーバIDが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 28

### 试验观点
タスク受け付け処理：入力パラメータの対象サーバIDがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 29

### 试验观点
タスク受け付け処理：入力パラメータの対象サーバIDが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 30

### 试验观点
タスク受け付け処理：入力パラメータのタスク種別が操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートのどれでもない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 31

### 试验观点
タスク受け付け処理：サーバ情報取得時DB読み取り失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 32

### 试验观点
タスク受け付け処理：サーバ情報取得時レコードが存在しない
"1. サーバ一覧画面から
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 33

### 试验观点
タスク受け付け処理：サーバ情報のAzure VM名がnull
"1. DBのサーバテーブルにAzure VM名がnullかつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 34

### 试验观点
タスク受け付け処理：サーバ情報のAzure VM名が空の文字列
"1. DBのサーバテーブルにAzure VM名が空の文字列かつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 35

### 试验观点
タスク受け付け処理：サーバ情報のDockerコンテナ名がnull
"1. DBのサーバテーブルにDockerコンテナ名がnullかつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 36

### 试验观点
タスク受け付け処理：サーバ情報のDockerコンテナ名が空の文字列
"1. DBのサーバテーブルにDockerコンテナ名が空の文字列かつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 37

### 试验观点
タスク受け付け処理：サーバ情報のHRWグループ名がnull
"1. DBのサーバテーブルにHRWグループ名がnullかつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 38

### 试验观点
タスク受け付け処理：サーバ情報のHRWグループ名が空の文字列
"1. DBのサーバテーブルにHRWグループ名が空の文字列かつ、ITDM2の統括マネージャーのレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 39

### 试验观点
タスク受け付け処理：コンテナ実行状態取得時DB読み取り失敗
"1. サーバ一覧画面から
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 40

### 试验观点
タスク受け付け処理：コンテナ実行状態のレコードが存在する、かつステータスがBUSY
"1. サーバ一覧画面から
"1. 「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（※{0}：対象サーバ名）のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 41

### 试验观点
タスク受け付け処理：コンテナ実行状態のレコードが存在する、かつステータスがBUSYまたはIDLE以外の値
"1. DBのコンテナ実行状態テーブルにステータスがBUSYまたはIDLE以外の値のレコードを作成する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 42

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が存在しない
"1. サーバ一覧画面から
"1. 「開始日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 43

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日がnull
"1. サーバ一覧画面から
"1. 「開始日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 44

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が空の文字列
"1. サーバ一覧画面から
"1. 「開始日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 45

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日がYYYY-MM-DDのフォーマットではない
"1. サーバ一覧画面から
"1. 「開始日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 46

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの開始日が実際に存在しない日付
"1. サーバ一覧画面から
"1. 「開始日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 47

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が存在しない
"1. サーバ一覧画面から
"1. 「終了日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 48

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日がnull
"1. サーバ一覧画面から
"1. 「終了日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 49

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が空の文字列
"1. サーバ一覧画面から
"1. 「終了日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 50

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日がYYYY-MM-DDのフォーマットではない
"1. サーバ一覧画面から
"1. 「終了日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 51

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が実際に存在しない日付
"1. サーバ一覧画面から
"1. 「終了日を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 52

### 试验观点
タスク受け付け処理：操作ログのエクスポートタスクの場合、入力パラメータの終了日が開始日より前の日付
"1. サーバ一覧画面から
"1. 「終了日は開始日以降の日付を指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 53

### 试验观点
タスク受け付け処理：値の一覧テーブルにOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のレコードがない
"1. DBの値の一覧テーブルにOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のレコードが存在しない状態にする。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 54

### 试验观点
タスク受け付け処理：値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueが数字ではない
"1. DBの値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueを数字でない値に変更する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 55

### 试验观点
タスク受け付け処理：操作ログのエクスポート最大期間がデフォルトの30日。指定された期間が30日のタスクを作成
"1. DBの値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueがデフォルトの30である。
"1. 「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 56

### 试验观点
タスク受け付け処理：操作ログのエクスポート最大期間がデフォルトの30日。指定された期間がデフォルトの最大期間30日を超えた
"1. DBの値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueがデフォルトの30である。
"1. 「30日を超える期間が指定されました。30日以内の期間を指定して再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 57

### 试验观点
タスク受け付け処理：操作ログのエクスポート最大期間が7日。指定された期間が7日のタスクを作成
"1. DBの値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueが7である。
"1. 「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 58

### 试验观点
タスク受け付け処理：操作ログのエクスポート最大期間が7日。指定された期間が最大期間7日を超えた
"1. DBの値の一覧テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN（操作ログのエクスポート期間最大日数）のvalueを30から7に変更する。
"1. 「7日を超える期間が指定されました。7日以内の期間を指定して再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 59

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルが存在しない
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 60

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルがnull
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 61

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのサイズが0
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 62

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名が存在しない
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 63

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名がnull
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 64

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータの元のファイル名が空の文字列
"1. サーバ一覧画面から
"1. 「ファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 65

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルの拡張子がcsvでない
"1. サーバ一覧画面から
"1. 「無効なファイル形式です。CSVファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 66

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのMIMEタイプがcsvでない
"1. サーバ一覧画面から
"1. 「無効なファイル形式です。CSVファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 67

### 试验观点
タスク受け付け処理：管理項目定義のインポートタスクの場合、入力パラメータのファイルのサイズが制限の10MBを超えた
"1. サーバ一覧画面から
"1. 「インポートできるファイルサイズの上限を超えています。10MB以下のファイルを指定してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 68

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFの値が不正
"1. 環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFの値をAzure Blob Storageに存在しないコンテナ名に変更する。
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 69

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGが存在しない
"1. 環境変数AZURE_STORAGE_CONNECTION_STRINGが存在しないように設定する。
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 70

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値がnull
"1. 環境変数AZURE_STORAGE_CONNECTION_STRINGの値をnullに変更する。
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 71

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値が空の文字列
"1. 環境変数AZURE_STORAGE_CONNECTION_STRINGの値を空の文字列に変更する。
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 72

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロード時に環境変数AZURE_STORAGE_CONNECTION_STRINGの値が不正
"1. 環境変数AZURE_STORAGE_CONNECTION_STRINGの値を実際のストレージアカウント名でない値に変更する。
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 73

### 试验观点
タスク受け付け処理：ファイルのBlobへのアップロードに失敗
"1. サーバ一覧画面から
"1. 「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 74

### 试验观点
タスク受け付け処理：接続不可によってコンテナ実行状態テーブルへの新規レコード作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。対象サーバはコンテナ実行状態テーブルに該当のレコードが存在しない。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 75

### 试验观点
タスク受け付け処理：コンテナ実行状態テーブルに該当のレコードが存在しない場合、同じサーバに対して複数のユーザーが同時にタスクを作成し、一番先のタスク受け付け処理がコンテナ実行状態テーブルへの新規レコード作成に成功し、残りのタスク受け付け処理が全部作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。対象サーバはコンテナ実行状態テーブルに該当のレコードが存在しない。
"1. 一番先のタスク受け付け処理がコンテナ実行状態テーブルへの新規レコード作成に成功し、受け付け完了のメッセージがポップアップで表示される。残りのタスク受け付け処理が全部コンテナ実行状態テーブルへの新規レコード作成に失敗し、「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 76

### 试验观点
タスク受け付け処理：タスクテーブルへの新規レコード作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 77

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数SERVICE_BUS_TASK_INPUT_QUEUE_NAMEの値が不正
"1. 環境変数SERVICE_BUS_TASK_INPUT_QUEUE_NAMEの値を実際のTaskInputQueueのキュー名でない値に変更する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 78

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEが存在しない
"1. 環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEが存在しないように設定する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 79

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値がnull
"1. 環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値をnullに変更する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 80

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値が空の文字列
"1. 環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値を空の文字列に変更する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 81

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージを送信する時に環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値が不正
"1. 環境変数AZURE_SERVICEBUS_NAMESPACE_HOSTNAMEの値を実際のServiceBusの名前空間名でない値に変更する。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 82

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージの送信に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 83

### 试验观点
タスク受け付け処理：未分類のエラーが発生
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 84

### 试验观点
タスク実行関数：タスク実行関数が処理成功の場合、タスク情報の更新
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「実行中」に変わり、開始日時が更新され、タスク詳細が空白に変わること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 85

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が10件。古いタスク記録のクリーンアップ処理を行わない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクの件数が10件であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 86

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 87

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「エラー」のステータス。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 88

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「中止」のステータス。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 89

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、今実行中のタスクを除いてすべて「正常終了」「エラー」「中止」のいずれかのステータス。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が5件削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクが古い順から5件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 90

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、すべて「正常終了」「エラー」「中止」以外のステータス。古いタスク記録のクリーンアップ処理を行わない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクの件数が11件で、削除が行われていないこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 91

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、すべて「正常終了」「エラー」「中止」以外のステータス。古いタスク記録のクリーンアップ処理を行わない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクの件数が15件で、削除が行われていないこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 92

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が15件で、その内今実行中のタスクを除いて3件のステータスが「正常終了」「エラー」「中止」のいずれか、11件のステータスが「正常終了」「エラー」「中止」以外。古いタスク記録のクリーンアップ処理が行われ、ステータスが「正常終了」「エラー」「中止」のいずれかの3件が削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクの中でステータスが「正常終了」「エラー」「中止」の3件が削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 93

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータスかつ関連ログファイルが1件の操作ログのエクスポートタスク。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。関連している1件の操作ログ記録も削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 94

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータスかつ操作ログのエクスポートタスク。その内一番古いタスクは3件の操作ログ記録と関連している。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。関連している3件の操作ログ記録も削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 95

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が13件で、今実行中のタスクを除いてすべて「正常終了」のステータスかつ操作ログのエクスポートタスク。その内一番古いタスクは3件の操作ログ記録と関連している。2, 3番目古いタスクは各1件の操作ログ記録と関連している。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が3件削除される。それぞれ関連している3件と1件と1件の操作ログ記録も削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 対象サーバと関連するタスクが古い順から3件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 96

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数が5件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が5件。古いタスク記録のクリーンアップ処理を行わない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクの件数が5件であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 97

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数が5件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が6件で、今実行中のタスクを除いてすべて「正常終了」のステータス。古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除される。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 対象サーバと関連するタスクが古い順から1件削除されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 98

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。サーバAが5件、サーバBが6件のタスク記録を持っていて、すべてが「正常終了」のステータス。サーバAの新規タスクでタスク実行関数が処理成功した後サーバAのタスク記録の件数が6件で、古いタスク記録のクリーンアップ処理が行われない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 古いタスク記録が削除されず、サーバAが6件、サーバBが6件のタスク記録を持っていること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 99

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。サーバAが10件、サーバBが6件のタスク記録を持っていて、すべてが「正常終了」のステータス。サーバAの新規タスクでタスク実行関数が処理成功した後サーバAのタスク記録の件数が11件で、古いタスク記録のクリーンアップ処理が行われ、古い順から1件削除される。サーバBがクリーンアップ処理の影響を受けず、6件のままである。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. サーバAのタスク記録が古い順から1件削除されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 100

### 试验观点
タスク実行関数：TaskInputQueueから受信したメッセージがnull
"1. Azure Service Busのポータル画面から、TaskInputQueueにnullのメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 101

### 试验观点
タスク実行関数：TaskInputQueueから受信したメッセージがundefined
"1. Azure Service Busのポータル画面から、TaskInputQueueにundefinedのメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 102

### 试验观点
タスク実行関数：TaskInputQueueから受信したメッセージが空の文字列
"1. Azure Service Busのポータル画面から、TaskInputQueueに空の文字列のメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 103

### 试验观点
タスク実行関数：TaskInputQueueから受信したメッセージがobject型ではない
"1. Azure Service Busのポータル画面から、TaskInputQueueにobject型でないメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 104

### 试验观点
タスク実行関数：TaskInputQueueから受信したタスクIDがnull
"1. Azure Service Busのポータル画面から、TaskInputQueueにタスクIDがnullのメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 105

### 试验观点
タスク実行関数：TaskInputQueueから受信したタスクIDがundefined
"1. Azure Service Busのポータル画面から、TaskInputQueueにタスクIDがundefinedのメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 106

### 试验观点
タスク実行関数：TaskInputQueueから受信したタスクIDが空の文字列
"1. Azure Service Busのポータル画面から、TaskInputQueueにタスクIDが空の文字列のメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 107

### 试验观点
タスク実行関数：TaskInputQueueから受信したタスクIDが文字列型ではない
"1. Azure Service Busのポータル画面から、TaskInputQueueにタスクIDが文字列型でない値のメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 108

### 试验观点
タスク実行関数：TaskInputQueueから受信したタスクIDがタスクテーブルに存在しない文字列
"1. Azure Service Busのポータル画面から、TaskInputQueueにタスクIDがタスクテーブルに存在しない文字列のメッセージを送信する。
1. タスク実行関数が起動し、ログ「[TaskExecuteFunc] タスクID {受信したタスクID} がデータベースに存在しません。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行関数

### 试验手順


### 确认项目


---

## 测试用例 109

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、DB読み取り失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. タスク実行関数がログ「[TaskExecuteFunc] タスクID {受信したタスクID} の処理中にエラー発生:{システムエラー情報}」と「[TaskExecuteFunc] タスクID または更新日時が不正のため、タスクステータス更新をスキップします」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 110

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、タスク種別が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 111

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、サーバIDが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 112

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、サーバ名がnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 113

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、サーバ名が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 114

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、VM名がnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 115

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、VM名が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 116

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、コンテナ名がnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 117

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、コンテナ名が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 118

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、HRWグループ名がnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 119

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、HRWグループ名が空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 120

### 试验观点
タスク実行関数：タスクテーブルからタスク情報取得時、タスクのステータスがQUEUED以外（ユーザーが先に中止操作を行った）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. タスク実行関数がログ「[TaskExecuteFunc] タスクID {受信したタスクID} のステータスがQUEUEDではない（現状: {現在のステータス}）。処理対象外。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 121

### 试验观点
タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、DB読み取り失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 122

### 试验观点
タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、レコードが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 123

### 试验观点
タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、ステータスが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 124

### 试验观点
タスク実行関数：コンテナ実行状態テーブルからコンテナ実行状態の情報取得時、ステータスがBUSY（コンテナ実行状態テーブルに該当のレコードが存在している場合、同じサーバに対して複数のユーザーが同時にタスクを作成し、一番先のタスク実行関数がコンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新した後、残りのタスク実行関数が同じレコードのステータスを取得する）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。対象サーバはコンテナ実行状態テーブルに該当のレコードが存在している。
1. 一番先のタスク実行関数の処理が正常に完了して、対象タスクのステータスが最後に「正常終了」に変わるが、残りのタスク実行関数の対象タスクは全部ステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（※{0}：対象サーバ名）であること。（Azureに出力されたログでコンテナ実行状態がBUSYであることを取得した場合とコンテナ実行状態テーブルのレコードを更新した結果が0件の場合を区別する）

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 125

### 试验观点
タスク実行関数：コンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新する時、更新した結果が0件（コンテナ実行状態テーブルに該当のレコードが存在している場合、同じサーバに対して複数のユーザーが同時にタスクを作成し、一番先のタスク実行関数がコンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新した後、残りのタスク実行関数が同じレコードの更新を行う）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。対象サーバはコンテナ実行状態テーブルに該当のレコードが存在している。
1. 一番先のタスク実行関数の処理が正常に完了して、対象タスクのステータスが最後に「正常終了」に変わるが、残りのタスク実行関数の対象タスクは全部ステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（※{0}：対象サーバ名）であること。（Azureに出力されたログでコンテナ実行状態がBUSYであることを取得した場合とコンテナ実行状態テーブルのレコードを更新した結果が0件の場合を区別する）

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 126

### 试验观点
タスク実行関数：コンテナ実行状態テーブルから該当のレコードのステータスをBUSYに更新する時、DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 127

### 试验观点
タスク実行関数：Azure Filesの作業ディレクトリtaskworkspacesの作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 128

### 试验观点
タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/の作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 129

### 试验观点
タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/imports/の作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 130

### 试验观点
タスク実行関数：Azure Filesの作業ディレクトリtaskworkspaces/<taskId>/exports/の作成に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 131

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメータにあるファイルのBlobパスのパラメータimportedFileBlobPathが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 132

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメータにあるファイルのBlobパスのパラメータimportedFileBlobPathがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 133

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメータにあるファイルのBlobパスのパラメータimportedFileBlobPathが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 134

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、タスクパラメータにあるファイルのBlobパスのパラメータimportedFileBlobPathが実際に存在しないBlobパス
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 135

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 136

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 137

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 138

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが実際に存在しないBlobコンテナ
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 139

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、Azure Blob Storage側にエラーが発生しコピー失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 140

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、Azure Blob StorageからAzure Filesへ一時CSVファイルをコピーする時、Azure Files側にエラーが発生しコピー失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 141

### 试验观点
タスク実行関数：タスクテーブルから該当のレコードのステータスをRUNBOOK_SUBMITTEDに更新する時、DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 142

### 试验观点
タスク実行関数：タスクテーブルから該当のレコードのステータスをRUNBOOK_SUBMITTEDに更新する時、更新した結果が0件（ユーザーが先に中止操作を行った）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行関数がログ「[TaskExecuteFunc] タスクID {受信したタスクID} は他プロセスにより変更・キャンセルされました。補償処理を実行します。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 143

### 试验观点
タスク実行関数：タスクテーブルから取得したタスク情報のタスク種別が操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートのどれでもない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 144

### 试验观点
タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 145

### 试验观点
タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 146

### 试验观点
タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 147

### 试验观点
タスク実行関数：操作ログのエクスポートタスクで、環境変数RUNBOOK_OPLOG_EXPORTが実際に存在しないRunbook名
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 148

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 149

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 150

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 151

### 试验观点
タスク実行関数：管理項目定義のインポートタスクで、環境変数RUNBOOK_MGMT_ITEM_IMPORTが実際に存在しないRunbook名
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 152

### 试验观点
タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 153

### 试验观点
タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTがnull
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 154

### 试验观点
タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが空の文字列
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 155

### 试验观点
タスク実行関数：管理項目定義のエクスポートタスクで、環境変数RUNBOOK_MGMT_ITEM_EXPORTが実際に存在しないRunbook名
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 156

### 试验观点
タスク実行関数：タスクテーブルから取得したタスク情報のHRWグループ名が実際に存在しないグループ名
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 157

### 试验观点
タスク実行関数：Azure AutomationのRunbookジョブ作成APIの呼び出しに失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 158

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBの値の一覧テーブルからタスク記録の最大保持件数を取得する時DB読み取り失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 159

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBの値の一覧テーブルから取得したタスク記録の最大保持件数の値が数字ではない。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 160

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBのタスクテーブルから対象サーバのタスク記録の件数を取得する時DB読み取り失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 161

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBのタスクテーブルから削除対象のタスク記録を取得する時DB読み取り失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 162

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBの操作ログテーブルから削除対象のタスクと関連する操作ログ記録を削除する時DB書き込み失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 163

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが存在しないため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 164

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFがnullのため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 165

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが空の文字列のため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 166

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFが実際に存在しないBlobコンテナ名のため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 167

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_OPLOGSが存在しないため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 168

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_OPLOGSがnullのため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 169

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_OPLOGSが空の文字列のため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 170

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中に環境変数AZURE_STORAGE_CONTAINER_OPLOGSが実際に存在しないBlobコンテナ名のため参照エラー。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 171

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にBlobファイルの削除に失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 172

### 试验观点
タスク実行関数：サーバごとのタスク記録の最大保持件数がデフォルトの10件。タスク実行関数が処理成功した後対象サーバのタスク記録の件数が11件で、今実行中のタスクを除いてすべて「正常終了」のステータス。本来は古いタスク記録のクリーンアップ処理が行われ、古い順からタスク記録が1件削除されるべきだが、クリーンアップ処理中にDBのタスクテーブルから削除対象のタスクを削除する時DB書き込み失敗。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクがクリーンアップ処理失敗の影響を受けず、タスクステータスが「実行待ち」から「実行中」に更新されたこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 173

### 试验观点
タスク実行関数：処理中に未分類のエラーが発生
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 174

### 试验观点
"タスク実行関数：タイムアウト時間が5分の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。
"サーバ一覧画面
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行関数のタイムアウト時間が5分であること。

### 试验对象

### 试验手順


### 确认项目


---

## 测试用例 175

### 试验观点
"タスク実行関数：エラー発生時の補償処理のDB更新に失敗の場合の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。
"サーバ一覧画面
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行関数がログ「[TaskExecuteFunc] タスクID {対象タスクのID} の処理中にエラー発生:{エラー情報}」を出力して終了すること。

### 试验对象

### 试验手順


### 确认项目


---

## 测试用例 176

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがnull
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにnullのメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 177

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがundefined
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにundefinedのメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 178

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージが空の文字列
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQに空の文字列のメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 179

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したメッセージがobject型ではない
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにobject型でないメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 180

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがnull
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにタスクIDがnullのメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 181

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがundefined
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにタスクIDがundefinedのメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 182

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDが空の文字列
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにタスクIDが空の文字列のメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 183

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDが文字列型ではない
"1. Azure Service Busのポータル画面から、TaskInputQueueのDLQにタスクIDが文字列型でない値のメッセージを送信する。
1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "タスク実行タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 184

### 试验观点
タスク実行タイムアウト関数：Azure Automationのジョブ取得APIの呼び出しに失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数がログ「[TaskExecuteTimeoutFunc] Azure Automationジョブ取得API呼び出し失敗: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 185

### 试验观点
タスク実行タイムアウト関数：Runbookジョブ作成APIが呼び出された後タスク実行関数の実行時間が5分を超えてタイムアウトする場合、タスク実行タイムアウト関数が起動しDB更新せずにそのまま終了する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数が起動し、ログ「[TaskExecuteTimeoutFunc] Azure Automationジョブが存在するため補償処理は不要です: {受信したタスクID}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 186

### 试验观点
タスク実行タイムアウト関数：Azure Files上のタスク作業ディレクトリ（taskworkspaces/<taskId>/）の削除に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 187

### 试验观点
タスク実行タイムアウト関数：タスクテーブルからタスク情報を取得する時DB読み取り失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数がログ「[TaskExecuteTimeoutFunc] 予期せぬ内部エラーが発生しました: {エラー情報}」と「[TaskExecuteTimeoutFunc] タスクID {対象タスクのID} の処理を終了します。」を出力した後終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 188

### 试验观点
タスク実行タイムアウト関数：TaskInputQueueのDLQから受信したタスクIDがタスクテーブルに存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数がログ「[TaskExecuteTimeoutFunc] taskId={対象タスクのID} のタスクがデータベースに存在しません。処理を終了します。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 189

### 试验观点
タスク実行タイムアウト関数：コンテナ実行状態テーブルの更新に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 190

### 试验观点
タスク実行タイムアウト関数：コンテナ実行状態テーブルの更新結果が0件（該当コンテナのステータスがIDLEのためタスク実行タイムアウト関数の更新条件と一致しない）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 191

### 试验观点
タスク実行タイムアウト関数：タスクのステータスがPENDING_CANCELLATIONまたはCANCELLED
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. ログ「[TaskExecuteTimeoutFunc] タスクステータスが {現在のタスクステータス} のため、EMET0005エラー更新をスキップします: {受信したタスクID}」と「[TaskExecuteTimeoutFunc] タスクID {受信したタスクID} のタイムアウト補償処理が正常に完了しました。」が出力された後タスク実行タイムアウト関数が終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 192

### 试验观点
タスク実行タイムアウト関数：タスクテーブルの更新に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. ログ「[TaskExecuteTimeoutFunc] タスクテーブル更新失敗: {エラー情報}」と「[TaskExecuteTimeoutFunc] タスクID {受信したタスクID} のタイムアウト補償処理が正常に完了しました。」が出力された後タスク実行タイムアウト関数が終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 193

### 试验观点
タスク実行タイムアウト関数：タスクテーブルの更新結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. ログ「[TaskExecuteTimeoutFunc] タスクテーブル更新件数0件: {受信したタスクID}」と「[TaskExecuteTimeoutFunc] タスクID {受信したタスクID} のタイムアウト補償処理が正常に完了しました。」が出力された後タスク実行タイムアウト関数が終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 194

### 试验观点
タスク実行タイムアウト関数：予期せぬ内部エラーが発生
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数がログ「[TaskExecuteTimeoutFunc] 予期せぬ内部エラーが発生しました: {エラー情報}」と「[TaskExecuteTimeoutFunc] タスクID {対象タスクのID} の処理を終了します。」を出力した後終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 195

### 试验观点
タスク実行タイムアウト関数：タイムアウト時間が5分の確認。及び関数が異常終了した後の仕様確認。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行タイムアウト関数のタイムアウト時間が5分であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 196

### 试验观点
Runbookジョブ監視関数：環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）がデフォルトの30秒
1. 30秒ごとにRunbookジョブ監視関数が起動され、ログ「[RunbookMonitorFunc] Runbookジョブ監視関数開始」が出力されること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）がデフォルトの30秒。

### 确认项目


---

## 测试用例 197

### 试验观点
Runbookジョブ監視関数：環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）が60秒
1. 60秒ごとにRunbookジョブ監視関数が起動され、ログ「[RunbookMonitorFunc] Runbookジョブ監視関数開始」が出力されること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数RUNBOOK_MONITOR_INTERVAL_SECONDS（タイマートリガーの時間間隔）が60秒。

### 确认项目


---

## 测试用例 198

### 试验观点
Runbookジョブ監視関数：実行中のRunbookジョブ監視関数がある場合新たに起動されない（Runbookジョブ監視関数の複数同時実行が発生しない）
"1. 本来Runbookジョブ監視関数が30秒ごとのタイマートリガーによって起動されるはずのタイミングでも、新たに起動されないこと。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. Runbookジョブ監視関数の実行時間が1分以上になるように調整する。

### 确认项目


---

## 测试用例 199

### 试验观点
Runbookジョブ監視関数：環境変数AZURE_AUTOMATION_ACCOUNT_NAMEがnull/undefined/空の文字列
"1. Runbookジョブ監視関数から「RunbookMonitorFuncに必要な環境変数が不足しています」の例外がスローされること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数AZURE_AUTOMATION_ACCOUNT_NAMEにnull/undefined/空の文字列を設定する。

### 确认项目


---

## 测试用例 200

### 试验观点
Runbookジョブ監視関数：環境変数SUBSCRIPTION_IDがnull/undefined/空の文字列
"1. Runbookジョブ監視関数から「RunbookMonitorFuncに必要な環境変数が不足しています」の例外がスローされること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数SUBSCRIPTION_IDにnull/undefined/空の文字列を設定する。

### 确认项目


---

## 测试用例 201

### 试验观点
Runbookジョブ監視関数：環境変数RESOURCE_GROUP_NAMEがnull/undefined/空の文字列
"1. Runbookジョブ監視関数から「RunbookMonitorFuncに必要な環境変数が不足しています」の例外がスローされること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数RESOURCE_GROUP_NAMEにnull/undefined/空の文字列を設定する。

### 确认项目


---

## 测试用例 202

### 试验观点
Runbookジョブ監視関数：環境変数SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAMEがnull/undefined/空の文字列
"1. Runbookジョブ監視関数から「RunbookMonitorFuncに必要な環境変数が不足しています」の例外がスローされること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. 環境変数SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAMEにnull/undefined/空の文字列を設定する。

### 确认项目


---

## 测试用例 203

### 试验观点
Runbookジョブ監視関数：タスクテーブルからRUNBOOK_SUBMITTEDステータスのレコードを取得する時DB読み取り失敗
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] Taskテーブルからのデータ取得失敗または致命的エラー: {エラー情報}」を出力した後終了すること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. Azure FunctionsとAzure SQL Databaseの接続を切断する。

### 确认项目


---

## 测试用例 204

### 试验观点
Runbookジョブ監視関数：タスクテーブルからRUNBOOK_SUBMITTEDステータスのレコードを取得した結果が0件
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] RUNBOOK_SUBMITTEDステータスのタスクが存在しません。処理を終了します。」を出力した後終了すること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. ステータスが実行中（RUNBOOK_SUBMITTED）のタスクがないことを確認。

### 确认项目


---

## 测试用例 205

### 试验观点
Runbookジョブ監視関数：タスクテーブルから取得したタスクの開始日時がnull
"1. Runbookジョブ監視関数が開始日時がnullのタスクレコードを処理する時ログ「[RunbookMonitorFunc] タスク{対象タスクのID}はstartedAt未設定のためスキップ」を出力して、処理をスキップすること。

### 试验对象
- Runbookジョブ監視関数

### 试验手順
"1. タスクテーブルにステータスがRUNBOOK_SUBMITTEDで、開始日時がnullのレコードを作成する。

### 确认项目


---

## 测试用例 206

### 试验观点
Runbookジョブ監視関数：環境変数RUNBOOK_TIMEOUT_SECONDS（Runbookジョブの最大実行許容時間）が1時間。タスクの開始日時から経過した時間が1時間を超えた後Runbookジョブ監視関数がRunbookジョブのタイムアウトを検知する。
"1. 環境変数RUNBOOK_TIMEOUT_SECONDS（Runbookジョブの最大実行許容時間の秒数）が3600。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}はタイムアウトしました ({タスクの開始日時から経過した秒数}s > 3600s)」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 207

### 试验观点
Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIの呼び出しに失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のAzure Automation API呼び出し失敗: {エラー情報}」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 208

### 试验观点
Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIの取得結果はジョブが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のジョブが存在しません（404）」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 209

### 试验观点
Runbookジョブ監視関数：タスクの開始日時から経過した時間が5時間以内で、Azure Automationのジョブ取得APIで取得したジョブのステータスがNew, Activating, Running, Blocked, Disconnectedのいずれか
"1. 環境変数RUNBOOK_TIMEOUT_SECONDS（Runbookジョブの最大実行許容時間の秒数）がデフォルトの18000。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}は実行中状態のため処理対象外: {New, Activating, Running, Blocked, Disconnectedのいずれか}」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 210

### 试验观点
Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、タスクテーブルからステータスをRUNBOOK_PROCESSINGに更新する時DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のDB更新失敗: {エラー情報}」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 211

### 试验观点
Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、タスクテーブルからステータスをRUNBOOK_PROCESSINGに更新した結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}の楽観ロック失敗：他プロセスによりタスクが変更されました」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 212

### 试验观点
Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タスクテーブルからステータスをRUNBOOK_SUBMITTEDに戻す
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のRunbookStatusQueue送信失敗: {エラー情報}」を出力して、タスクテーブルからタスク1のステータスをRUNBOOK_SUBMITTEDに戻した後タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 213

### 试验观点
Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タスクテーブルからステータスをRUNBOOK_SUBMITTEDに戻す時DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のRunbookStatusQueue送信失敗: {エラー情報}」と「[RunbookMonitorFunc] タスク{対象タスクのID}のステータス戻し失敗: {エラー情報}」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 214

### 试验观点
Runbookジョブ監視関数：Runbookジョブ処理関数で処理する必要のあるタスクに対して、RunbookStatusQueueにメッセージを送信失敗して、タスクテーブルからステータスをRUNBOOK_SUBMITTEDに戻す時更新件数が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のRunbookStatusQueue送信失敗: {エラー情報}」と「[RunbookMonitorFunc] タスク{対象タスクのID}のステータス戻し失敗：タスクが既に他の状態に変更されています」を出力して、タスク1の処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 215

### 试验观点
Runbookジョブ監視関数：タイムアウト時間が5分の確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数のタイムアウト時間が5分であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 216

### 试验观点
Runbookジョブ監視関数：RUNBOOK_SUBMITTEDステータスのタスクを処理する時に予期せぬ内部エラーが発生
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数がログ「[RunbookMonitorFunc] タスク{対象タスクのID}のRunbookStatusQueue送信失敗: {エラー情報}」と「[RunbookMonitorFunc] タスク{対象タスクのID}のステータス戻し失敗：タスクが既に他の状態に変更されています」を出力して、対象タスクの処理をスキップすること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 217

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがnull
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにnullのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] メッセージが不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 218

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがundefined
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにundefinedのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] メッセージが不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 219

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージが空の文字列
"1. Azure Service Busのポータル画面から、RunbookStatusQueueに空の文字列のメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] メッセージが不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 220

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したメッセージがobject型ではない
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにobject型でないメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] メッセージが不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 221

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDがnull
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにタスクIDがnullのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 222

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDがundefined
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにタスクIDがundefinedのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 223

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDが空の文字列
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにタスクIDが空の文字列のメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 224

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したタスクIDが文字列型ではない
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにタスクIDが文字列型でない値のメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 225

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがnull
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにジョブステータスがnullのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 226

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがundefined
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにジョブステータスがundefinedのメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 227

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスが空の文字列
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにジョブステータスが空の文字列のメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 228

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスが文字列型ではない
"1. Azure Service Busのポータル画面から、RunbookStatusQueueにジョブステータスが文字列型でない値のメッセージを送信する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 229

### 试验观点
Runbookジョブ処理関数：タスクテーブルからタスク情報を取得する時DB読み取り失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 230

### 试验观点
Runbookジョブ処理関数：タスクテーブルからタスク情報を取得した結果は該当のタスクレコードが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] タスクID {対象タスクのID} がデータベースに存在しません。」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 231

### 试验观点
Runbookジョブ処理関数：対象タスクのステータスがRUNBOOK_PROCESSINGではない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] タスクID {対象タスクのID} のステータスが RUNBOOK_PROCESSING ではありません（現状: {実際のタスクステータス}）。処理を終了します。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 232

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例外が発生していない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行中」から「正常終了」に変わり、終了日時が更新され、タスク詳細が「操作ログ一覧画面で操作ログファイルをダウンロードしてください。ログ名は{タスク名}_{連番}.zipです。」に変わること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 233

### 试验观点
Runbookジョブ処理関数：管理項目定義のインポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例外が発生していない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行中」から「正常終了」に変わり、終了日時が更新され、タスク詳細が空白のままであること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 234

### 试验观点
Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompleted、Runbookジョブ処理関数の処理に例外が発生していない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. 作成されたタスクのステータスが「実行中」から「正常終了」に変わり、終了日時が更新され、タスク詳細が「ダウンロード」のリンクに変わること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 235

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspaces/<taskId>/exports/が存在しない場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Files操作失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 236

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspaces/<taskId>/exports/にファイルもサブディレクトリも存在しない場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] エクスポートファイルなし: taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 237

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、作業ディレクトリtaskworkspaces/<taskId>/exports/に操作ログファイル（exportoplog_*.zip）が存在せず、他のファイルが存在している場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] エクスポートファイルなし: taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 238

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログファイルのコピーに失敗（Azure Filesと接続できない）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Files操作失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 239

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログファイルのコピーに失敗（Azure Blob Storageと接続できない）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Files操作失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 240

### 试验观点
Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、目標ファイルtaskworkspaces/<taskId>/exports/assetsfield_def.csvが存在しない場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 管理項目定義ファイルなし: taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 241

### 试验观点
Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、管理項目定義ファイルのコピーに失敗（Azure Filesと接続できない）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Files操作失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 242

### 试验观点
Runbookジョブ処理関数：管理項目定義のエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、管理項目定義ファイルのコピーに失敗（Azure Blob Storageと接続できない）
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「管理項目定義のエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Files操作失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 243

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがCompletedで、タスク種別が操作ログのエクスポート、管理項目定義のエクスポート、管理項目定義のインポートのどれでもない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 244

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在している場合。1回目ではAzure Filesと接続できなかった。2回目でAzure Filesと接続できた。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 245

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txtが存在しない場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 基盤スクリプト起動エラー: taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 246

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在している場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 基盤スクリプト実行エラー: taskId={対象タスクのID}, detail={errordetail.txtの内容}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 247

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空である）が存在している場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 基盤スクリプト実行エラー: taskId={対象タスクのID}, detail={errordetail.txtの内容}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 248

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードSJIS、内容が空ではない）が存在している場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 基盤スクリプト実行エラー: taskId={対象タスクのID}, detail={errordetail.txtの内容}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 249

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがRemoving / Stopped / Stoppingのいずれか
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] メンテナンス状態: {実際のジョブステータス}, taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 250

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがResuming / Suspended / Suspendingのいずれかの時、RunbookジョブストップAPI呼び出し失敗
"0. Azure AutomationにPowerShell Workflow Runbookを作成して、一時的に通常のPowerShell Runbookの代わりにする。（PowerShell Workflow RunbookでないとResume / Suspend操作ができない）
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] ジョブ停止失敗: {エラー情報}」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 251

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがResuming / Suspended / Suspendingのいずれかの時、RunbookジョブストップAPI呼び出し成功
"0. Azure AutomationにPowerShell Workflow Runbookを作成して、一時的に通常のPowerShell Runbookの代わりにする。（PowerShell Workflow RunbookでないとResume / Suspend操作ができない）
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] ジョブ停止完了: {実際のジョブステータス}, taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 252

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがTimeoutの時、RunbookジョブストップAPI呼び出し失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] タイムアウト時ジョブ停止失敗: {エラー情報}」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 253

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがTimeoutの時、RunbookジョブストップAPI呼び出し成功
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] タイムアウト処理完了: taskId={対象タスクのID}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 254

### 试验观点
Runbookジョブ処理関数：RunbookStatusQueueから受信したジョブステータスがCompleted / Failed / Removing / Stopped / Stopping / Resuming / Suspended / Suspending / Timeoutのどれでもない
"1. DBのタスクテーブルにステータスがRUNBOOK_PROCESSINGのレコードを作成する。
"1. Runbookジョブ処理関数が起動し、ログ「[RunbookProcessorFunc] 予期しないステータス: {実際のジョブステータス}, taskId={対象タスクのID}」と「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理関数

### 试验手順


### 确认项目


---

## 测试用例 255

### 试验观点
Runbookジョブ処理関数：Azure Files上の作業ディレクトリtaskworkspaces/<taskId>/の削除に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Azure Files作業ディレクトリ削除失敗: {エラー情報}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 256

### 试验观点
Runbookジョブ処理関数：Azure Files上の作業ディレクトリtaskworkspaces/<taskId>/の削除に成功
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] Azure Files作業ディレクトリ削除成功: taskworkspaces/{対象タスクのID}/」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 257

### 试验观点
Runbookジョブ処理関数：タスクテーブルの更新時にDB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 258

### 试验观点
Runbookジョブ処理関数：タスクテーブルの更新結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 259

### 试验观点
Runbookジョブ処理関数：操作ログのエクスポートタスク、かつRunbookジョブのステータスがCompletedの時、操作ログテーブルへのレコード作成に失敗の場合
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 260

### 试验观点
Runbookジョブ処理関数：コンテナ実行状態テーブルの更新時にDB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 261

### 试验观点
Runbookジョブ処理関数：コンテナ実行状態テーブルの更新結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 262

### 试验观点
Runbookジョブ処理関数：タイムアウト時間が30分の確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数のタイムアウト時間が30分であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 263

### 试验观点
"Runbookジョブ処理関数：リトライ回数が2（RunbookStatusQueueの最大配信数が3）の確認
"サーバ一覧画面
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 処理中にエラーが発生しました: {エラー情報}」を出力して終了すること。

### 试验对象

### 试验手順


### 确认项目


---

## 测试用例 264

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがnull
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにnullのメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 265

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがundefined
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにundefinedのメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 266

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージが空の文字列
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQに空の文字列のメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 267

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したメッセージがobject型ではない
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにobject型でないメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] メッセージが不正です。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 268

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDがnull
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにタスクIDがnullのメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] taskIdが特定できません。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 269

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDがundefined
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにタスクIDがundefinedのメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] taskIdが特定できません。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 270

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDが空の文字列
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにタスクIDが空の文字列のメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] taskIdが特定できません。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 271

### 试验观点
Runbookジョブ処理タイムアウト関数：RunbookStatusQueueのDLQから受信したタスクIDが文字列型ではない
"1. Azure Service Busのポータル画面から、RunbookStatusQueueのDLQにタスクIDが文字列型でない値のメッセージを送信する。
1. Runbookジョブ処理タイムアウト関数が起動し、ログ「[RunbookProcessorTimeoutFunc] taskIdが特定できません。処理を終了します。」を出力して終了すること。

### 试验对象
- "Runbookジョブ処理タイムアウト関数

### 试验手順


### 确认项目


---

## 测试用例 272

### 试验观点
Runbookジョブ処理タイムアウト関数：タスクテーブルからタスク情報を取得する時レコードが存在しない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] taskId={対象タスクのID} のタスクがデータベースに存在しません。処理を終了します。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 273

### 试验观点
Runbookジョブ処理タイムアウト関数：タスクステータスがRUNBOOK_PROCESSINGではない
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] タスク{対象タスクのID}のステータスが{実際のタスクステータス}のため処理対象外です。処理を終了します。」を出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 274

### 试验观点
Runbookジョブ処理タイムアウト関数：コンテナ実行状態テーブル更新時DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] DBトランザクション失敗: {エラー情報}」と「[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除成功: taskworkspaces/{対象タスクのID}/」と「[RunbookProcessorTimeoutFunc] タスクID {対象タスクのID} のタイムアウト補償処理が正常に完了しました。」を出力して終了すること（DBトランザクション失敗でも処理継続すること）。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 275

### 试验观点
Runbookジョブ処理タイムアウト関数：コンテナ実行状態テーブル更新時更新結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] コンテナ実行状態テーブル更新失敗: 更新件数0件」と「[RunbookProcessorTimeoutFunc] DBトランザクション失敗: {エラー情報}」と「[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除成功: taskworkspaces/{対象タスクのID}/」と「[RunbookProcessorTimeoutFunc] タスクID {対象タスクのID} のタイムアウト補償処理が正常に完了しました。」を出力して終了すること（DBトランザクション失敗でも処理継続すること）。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 276

### 试验观点
Runbookジョブ処理タイムアウト関数：タスクテーブル更新時DB書き込み失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] DBトランザクション失敗: {エラー情報}」と「[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除成功: taskworkspaces/{対象タスクのID}/」と「[RunbookProcessorTimeoutFunc] タスクID {対象タスクのID} のタイムアウト補償処理が正常に完了しました。」を出力して終了すること（DBトランザクション失敗でも処理継続すること）。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 277

### 试验观点
Runbookジョブ処理タイムアウト関数：タスクテーブル更新時更新結果が0件
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] タスクテーブル更新失敗: 更新件数0件」と「[RunbookProcessorTimeoutFunc] DBトランザクション失敗: {エラー情報}」と「[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除成功: taskworkspaces/{対象タスクのID}/」と「[RunbookProcessorTimeoutFunc] タスクID {対象タスクのID} のタイムアウト補償処理が正常に完了しました。」を出力して終了すること（DBトランザクション失敗でも処理継続すること）。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 278

### 试验观点
Runbookジョブ処理タイムアウト関数：Azure Files上の作業ディレクトリの削除に失敗
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数がログ「[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除失敗: {エラー情報}」と「[RunbookProcessorTimeoutFunc] タスクID {対象タスクのID} のタイムアウト補償処理が正常に完了しました。」を出力して終了すること（作業ディレクトリ削除失敗でも処理継続すること）。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 279

### 试验观点
Runbookジョブ処理タイムアウト関数：タイムアウト時間が5分の確認、及び関数が異常終了した後の仕様確認
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数のタイムアウト時間が5分であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 280

### 试验观点
Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で実行時間が30分を超えてタイムアウトする場合、Runbookジョブ処理タイムアウト関数が起動しDBから対象タスクとコンテナのレコードを更新する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数が下記のログを出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 281

### 试验观点
Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で例外が発生する場合、Runbookジョブ処理タイムアウト関数が起動しDBから対象タスクとコンテナのレコードを更新する。
"1. サーバ一覧画面からITDM2の統括マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数が下記のログを出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 282

### 试验观点
複数ライセンスのユーザーが同時にタスクを実行する場合
"3人とも別々のライセンスのユーザーが同時に下記の操作を行う。
"3人のユーザーがそれぞれのライセンスで下記の事項を確認できること：

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 283

### 试验观点
開始日と終了日を同じ日に指定し、出力期間が1日の場合に操作ログのエクスポートタスクが正常終了する（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 284

### 试验观点
出力期間が7日、かつ開始日と終了日が同じ月の場合に操作ログのエクスポートタスクが正常終了する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 285

### 试验观点
出力期間が29日、かつ開始日と終了日が月を跨ぐ場合に操作ログのエクスポートタスクが正常終了する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 286

### 试验观点
出力期間がデフォルトの最大期間30日で、複数件の操作ログファイルが出力された場合、操作ログのエクスポートタスクが正常終了する（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「操作ログのエクスポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したこと。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 287

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」に「ああああ」を追加するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 288

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」の「ああああ」を「いいいい」に変更するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 289

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」の「いいいい」を削除するCSVファイルを選択し、管理項目定義のインポートタスクが正常終了する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→「管理項目定義のインポート」の順番でクリック。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 290

### 试验观点
ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加し、管理項目定義のエクスポートタスクが正常終了する。（中継マネージャ）
"1. 資産管理項目の設定画面で、ハードウェア資産情報の追加管理項目「テスト項目」に「ええええ」を追加する。
"1. タスクの受け付けが正常に完了したことを確認。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 291

### 试验观点
複数のユーザーが同時に同じサーバに対してタスクを実行する場合（中継マネージャ）
"3人のユーザーが同時に同じサーバに対して下記の操作を行う。
"1. 一人だけタスクの実行に成功し、タスクのステータスが「正常終了」になる。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 292

### 试验观点
Runbookジョブの実行時間（タスクの開始日時から経過した時間）が5時間を超えた場合タスクがタイムアウトしたと検知され、エラーに更新される（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。環境変数RUNBOOK_TIMEOUT_SECONDSがデフォルトの18000。
"1. 作成されたタスクのステータスが「実行中」から「エラー」に変わり、終了日時が開始日時から5時間経過した時刻に更新され、タスク詳細が空白から「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 293

### 试验观点
タスク受け付け処理：Azure Service Busのキューへタスク実行要求メッセージの送信に失敗（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」のエラーメッセージがポップアップで表示されること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 294

### 试验观点
タスク実行関数：Azure AutomationのRunbookジョブ作成APIの呼び出しに失敗（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. 作成されたタスクのステータスが「実行待ち」から「エラー」に変わり、タスク詳細が「中止する」ボタンから「エラー詳細を表示」のリンクに変わり、エラー詳細の内容が「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 295

### 试验观点
"タスク実行関数：タイムアウト時間が5分の確認。リトライしないこと（TaskInputQueueの最大配信数が1）の確認。
"サーバ一覧画面
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. タスク実行関数のタイムアウト時間が5分であること。

### 试验对象

### 试验手順


### 确认项目


---

## 测试用例 296

### 试验观点
Runbookジョブ監視関数：タイムアウト時間が5分の確認（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ監視関数のタイムアウト時間が5分であること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 297

### 试验观点
Runbookジョブ処理関数：RunbookジョブのステータスがFailedの時、errordetail.txt（文字コードUTF-8、内容が空ではない）が存在している場合（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理関数がログ「[RunbookProcessorFunc] 基盤スクリプト実行エラー: taskId={対象タスクのID}, detail={errordetail.txtの内容}」を出力すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

## 测试用例 298

### 试验观点
Runbookジョブ処理タイムアウト関数：Runbookジョブ処理関数の3回目の実行で実行時間が30分を超えてタイムアウトする場合、Runbookジョブ処理タイムアウト関数が起動しDBから対象タスクとコンテナのレコードを更新する。（中継マネージャ）
"1. サーバ一覧画面からITDM2の中継マネージャーに対して、「タスクを選択」→任意のタスクの順番でクリック。
"1. Runbookジョブ処理タイムアウト関数が下記のログを出力して終了すること。

### 试验对象
- "サーバ一覧画面

### 试验手順


### 确认项目


---

