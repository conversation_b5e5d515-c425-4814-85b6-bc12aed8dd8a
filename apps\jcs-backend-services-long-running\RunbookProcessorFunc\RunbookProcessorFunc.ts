/**
 * @fileoverview Runbookジョブ処理関数 (RunbookProcessorFunc)
 * @description
 * Azure Service BusのRunbookStatusQueueからRunbookジョブの実行結果メッセージを受信し、
 * タスクの最終的な後処理（成果物処理、状態更新、リソースクリーンアップ）を行う関数。
 *
 * @trigger Azure Service Bus - RunbookStatusQueue キューメッセージ
 * @input RunbookStatusQueue から受信するJSON形式のメッセージ
 * @output Azure SQL Database (Task、ContainerConcurrencyStatus、OperationLog) のレコード更新、
 *         Azure Blob Storageへの最終成果物ファイルのコピー、Azure Files上のタスク作業ディレクトリの削除
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext } from "@azure/functions";
import { Prisma, Task } from "@prisma/client";
import { prisma } from "../lib/prisma";
import {
  createBlobServiceClient,
  createShareServiceClient,
  stopAutomationJob,
} from "../lib/azureClients";
import { AppConstants } from "../lib/constants";
import {
  formatTaskErrorMessage,
  isAzureFilesError,
  isAzureBlobError,
  deleteTaskWorkspaceDirectory,
  logErrorWithStack
} from "../lib/utils";

// 型定義
/**
 * RunbookStatusQueueから受信するメッセージの型定義
 */
interface RunbookStatusMessage {
  taskId: string;
  automationJobStatus: string;
  exception?: string;
}

// 定数定義
// 成果物（管理項目定義）を保存するBlobコンテナ名
const ASSETS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;
// 成果物（操作ログ）を保存するBlobコンテナ名
const OPLOGS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_OPLOGS!;


/**
 * RunbookProcessorFunc - Runbookジョブ実行結果の最終処理
 *
 * 処理ステップ:
 * 1-2. RunbookStatusQueueメッセージ受信・解析、必須パラメータ検証
 * 3. タスク情報取得・ステータス確認（RUNBOOK_PROCESSING）
 * 4. automationJobStatusに基づく処理分岐
 * 5. Azure Files作業ディレクトリクリーンアップ
 * 6-9. DBトランザクション（タスク更新、コンテナ状態更新）
 * 10-11. エラー処理・正常終了
 *
 * エラー処理:
 * 必須パラメータ不足・タスク取得失敗時は例外throw（リトライ）、
 * RUNBOOK_PROCESSING以外は処理終了、その他エラーは適切なエラーコード設定。
 */
export async function RunbookProcessorFunc(message: unknown, context: InvocationContext): Promise<void> {
  let taskId: string | undefined;
  let task: Task | null = null;
  let originalTaskUpdatedAt: Date | undefined;

  try {
    // 1. Azure Service Bus の RunbookStatusQueue からメッセージを受信し、JSON形式から解析する
    context.log("[RunbookProcessorFunc] RunbookStatusQueue からメッセージ受信");

    // メッセージ基本検証
    if (!message || typeof message !== 'object') {
      const errorMsg = "[RunbookProcessorFunc] メッセージが不正です。";
      context.error(errorMsg);
      throw new Error(errorMsg);
    }

    // taskId, automationJobStatus, （任意）exceptionを抽出する
    const messageBody = message as RunbookStatusMessage;
    const { taskId: extractedTaskId, automationJobStatus, exception } = messageBody;

    // 2. 必須情報（taskId, automationJobStatus）が不足/不正の場合、エラー詳細をログに記録した後、例外をthrowする
    if (!extractedTaskId || typeof extractedTaskId !== "string" || !automationJobStatus || typeof automationJobStatus !== "string") {
      const errorMsg = "[RunbookProcessorFunc] 必須情報（taskId, automationJobStatus）が不足/不正です。";
      context.error(errorMsg);
      throw new Error(errorMsg);
    }

    taskId = extractedTaskId;
    context.log(`[RunbookProcessorFunc] 受信パラメータ: taskId=${taskId}, automationJobStatus=${automationJobStatus}`);

    // 3. taskId を使用して Task テーブルを検索し、タスクの関連情報を取得し、statusが RUNBOOK_PROCESSINGであるか判定する
    task = await prisma.task.findUnique({ where: { id: taskId } });

    if (!task) {
      // タスクが存在しない、または取得失敗の場合は、エラー詳細をログに記録した後、例外をthrowする
      const errorMsg = `[RunbookProcessorFunc] タスクID ${taskId} がデータベースに存在しません。`;
      context.error(errorMsg);
      throw new Error(errorMsg);
    }

    if (task.status !== AppConstants.TaskStatus.RunbookProcessing) {
      // タスクのstatusが RUNBOOK_PROCESSING でない場合、ログに記録して処理を終了する
      context.log(`[RunbookProcessorFunc] タスクID ${taskId} のステータスが RUNBOOK_PROCESSING ではありません（現状: ${task.status}）。処理を終了します。`);
      return;
    }

    // 楽観ロック制御のため、タスクレコードの最終更新日時を保存
    originalTaskUpdatedAt = task.updatedAt;
    context.log(`[RunbookProcessorFunc] タスク情報取得成功: taskId=${taskId}, taskType=${task.taskType}, licenseId=${task.licenseId}`);

    // 4. メッセージ内の automationJobStatus に基づき、処理が分岐する
    // （ステップ4.で記載されたDB書き込みは即時に行われるのではなく、書き込みのデータを記録して、ステップ6.-9.で実行されるDBトランザクション内でコンテナのステータスの更新と一緒に行われる）
    let taskUpdateData: Prisma.TaskUpdateInput = {};
    const operationLogsToCreate: Prisma.OperationLogCreateManyInput[] = [];

    // automationJobStatusに基づく処理分岐
    switch (automationJobStatus) {
      case "Completed":
        await handleCompletedStatus(task, taskId, context, taskUpdateData, operationLogsToCreate);
        break;

      case "Failed":
        await handleFailedStatus(task, taskId, context, taskUpdateData, exception);
        break;

      case "Removing":
      case "Stopped":
      case "Stopping":
        // automationJobStatus が Removing / Stopped / Stopping のいずれかの場合
        taskUpdateData = {
          status: AppConstants.TaskStatus.CompletedError,
          endedAt: new Date(),
          resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0010),
          errorCode: AppConstants.ERROR_CODES.EMET0010,
        };
        context.log(`[RunbookProcessorFunc] メンテナンス状態: ${automationJobStatus}, taskId=${taskId}`);
        break;

      case "Resuming":
      case "Suspended":
      case "Suspending":
        // automationJobStatus が Resuming / Suspended / Suspending のいずれかの場合
        try {
          await stopAutomationJob(taskId);
          taskUpdateData = {
            status: AppConstants.TaskStatus.CompletedError,
            endedAt: new Date(),
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0010),
            errorCode: AppConstants.ERROR_CODES.EMET0010,
          };
          context.log(`[RunbookProcessorFunc] ジョブ停止完了: ${automationJobStatus}, taskId=${taskId}`);
        } catch (stopError: any) {
          logErrorWithStack(context, `[RunbookProcessorFunc] ジョブ停止失敗:`, stopError);
          throw stopError; // リトライのため例外を再スロー
        }
        break;

      case "Timeout":
        // automationJobStatus が Timeout (RunbookMonitorFuncによる判断) の場合
        try {
          await stopAutomationJob(taskId);
          taskUpdateData = {
            status: AppConstants.TaskStatus.CompletedError,
            endedAt: new Date(),
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0005),
            errorCode: AppConstants.ERROR_CODES.EMET0005,
          };
          context.log(`[RunbookProcessorFunc] タイムアウト処理完了: taskId=${taskId}`);
        } catch (stopError: any) {
          logErrorWithStack(context, `[RunbookProcessorFunc] タイムアウト時ジョブ停止失敗:`, stopError);
          throw stopError; // リトライのため例外を再スロー
        }
        break;

      default:
        // その他のステータス - 予期しないステータスの場合は例外をスローしてリトライ
        context.error(`[RunbookProcessorFunc] 予期しないステータス: ${automationJobStatus}, taskId=${taskId}`);
        throw new Error(`予期しないautomationJobStatus: ${automationJobStatus}`);
    }

    // 5. Azure Files上のタスクIDごとのタスク作業ディレクトリをクリーンアップ（ディレクトリ全体を削除）する
    try {
      const shareClient = createShareServiceClient().getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
      const taskDirClient = shareClient.getDirectoryClient(taskId);
      await deleteTaskWorkspaceDirectory(taskDirClient, context);
      context.log(`[RunbookProcessorFunc] Azure Files作業ディレクトリ削除成功: TaskWorkspaces/${taskId}/`);
    } catch (cleanupError) {
      context.log(`[RunbookProcessorFunc] Azure Files作業ディレクトリ削除失敗: ${cleanupError}`);
    }

    // 6. DBトランザクションを開始する
    // 7. ステップ4.で記載されたDB書き込みを行う（タスクテーブルの更新）
    // 8. コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する
    // 9. トランザクションをコミットする
    await prisma.$transaction(async (tx) => {
      // 7. ステップ4.で記載されたDB書き込みを行う（タスクテーブルの更新）
      // 更新条件：ID = 入力パラメータのtaskId、最終更新日時 = ステップ3.で取得した最終更新日時
      const taskUpdateResult = await tx.task.updateMany({
        where: {
          id: taskId,
          updatedAt: originalTaskUpdatedAt // 楽観ロック条件
        },
        data: taskUpdateData,
      });

      // 楽観ロック失敗チェック
      if (taskUpdateResult.count === 0) {
        throw new Error(`タスク${taskId}の更新失敗: 楽観ロック失敗またはタスクが存在しない`);
      }

      // 操作ログレコードが存在する場合は作成
      if (operationLogsToCreate.length > 0) {
        await tx.operationLog.createMany({
          data: operationLogsToCreate,
        });
      }

      // 8. コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する
      // 条件：対象VM名 = ステップ3.で取得した対象VM名、対象コンテナ名 = ステップ3.で取得した対象コンテナ名、
      // ステータス（status） = BUSY、使用中のタスクID（currentTaskId） = 入力パラメータのtaskId
      const containerUpdateResult = await tx.containerConcurrencyStatus.updateMany({
        where: {
          targetVmName: task!.targetVmName!,
          targetContainerName: task!.targetContainerName!,
          status: "BUSY",
          currentTaskId: taskId,
        },
        data: {
          status: "IDLE",
          currentTaskId: null,
        },
      });

      // 更新件数が0件の場合は例外をスロー
      if (containerUpdateResult.count === 0) {
        throw new Error(`コンテナ状態更新失敗: 対象コンテナが見つからないか既に他の状態に変更されています (VM: ${task!.targetVmName}, Container: ${task!.targetContainerName})`);
      }

      context.log(`[RunbookProcessorFunc] DBトランザクション完了: taskId=${taskId}`);
    });

    // 11. 正常終了ログを記録し、処理を終了する（メッセージACK）
    context.log(`[RunbookProcessorFunc] タスクID ${taskId} の後処理が正常に完了しました。`);

  } catch (error) {
    // 10. ここまでの処理中にエラーが発生した場合の処理
    // エラー詳細をログに記録した後、例外をthrowする（リトライのため）
    logErrorWithStack(context, `[RunbookProcessorFunc] 処理中にエラーが発生しました:`, error);
    throw error;
  }
}

/**
 * automationJobStatus が "Completed" の場合の処理
 * タスクタイプに応じて成果物の処理を実行
 */
async function handleCompletedStatus(
  task: Task,
  taskId: string,
  context: InvocationContext,
  taskUpdateData: Prisma.TaskUpdateInput,
  operationLogsToCreate: Prisma.OperationLogCreateManyInput[]
): Promise<void> {
  try {
    switch (task.taskType) {
      case AppConstants.TaskType.OpLogExport:
        // a. Task.taskTypeが操作ログのエクスポート (TASK_TYPE.OPLOG_EXPORT) の場合
        await handleOplogExportCompleted(task, taskId, context, taskUpdateData, operationLogsToCreate);
        break;

      case AppConstants.TaskType.MgmtItemExport:
        // b. Task.taskTypeが管理項目定義のエクスポート (TASK_TYPE.MGMT_ITEM_EXPORT) の場合
        await handleMgmtItemExportCompleted(task, taskId, context, taskUpdateData);
        break;

      case AppConstants.TaskType.MgmtItemImport:
        // c. Task.taskTypeが管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) の場合
        Object.assign(taskUpdateData, {
          status: AppConstants.TaskStatus.CompletedSuccess,
          endedAt: new Date(),
        });
        context.log(`[RunbookProcessorFunc] 管理項目定義インポート完了: taskId=${taskId}`);
        break;

      default:
        throw new Error(`未対応のタスク種別: ${task.taskType}`);
    }
  } catch (error: any) {
    // Blob操作エラーまたはFiles操作エラーの場合
    if (isAzureBlobError(error)) {
      Object.assign(taskUpdateData, {
        status: AppConstants.TaskStatus.CompletedError,
        endedAt: new Date(),
        resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0003),
        errorCode: AppConstants.ERROR_CODES.EMET0003,
        errorMessage: error.message,
      });
      logErrorWithStack(context, `[RunbookProcessorFunc] Blob操作失敗:`, error);
    } else if (isAzureFilesError(error)) {
      Object.assign(taskUpdateData, {
        status: AppConstants.TaskStatus.CompletedError,
        endedAt: new Date(),
        resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0002),
        errorCode: AppConstants.ERROR_CODES.EMET0002,
        errorMessage: error.message,
      });
      logErrorWithStack(context, `[RunbookProcessorFunc] Files操作失敗:`, error);
    } else {
      // その他のエラー
      throw error;
    }
  }
}

/**
 * 操作ログエクスポート完了処理
 * exportoplog_*.zipファイルをAzure Blob Storageにコピーし、OperationLogレコードを作成
 */
async function handleOplogExportCompleted(
  task: Task,
  taskId: string,
  context: InvocationContext,
  taskUpdateData: Prisma.TaskUpdateInput,
  operationLogsToCreate: Prisma.OperationLogCreateManyInput[]
): Promise<void> {
  // i. Azure Filesワークスペースのエクスポートディレクトリ TaskWorkspaces/<taskId>/exports/ から、パターン exportoplog_*.zip に一致する全てのファイルを取得する
  const shareClient = createShareServiceClient().getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
  const exportsDir = shareClient.getDirectoryClient(`${taskId}/exports`);

  const files = [];
  for await (const item of exportsDir.listFilesAndDirectories()) {
    if (item.kind === "file" && item.name.startsWith("exportoplog_") && item.name.endsWith(".zip")) {
      files.push(item.name);
    }
  }

  // ii. 一致するファイルが存在しない場合
  if (files.length === 0) {
    Object.assign(taskUpdateData, {
      status: AppConstants.TaskStatus.CompletedError,
      endedAt: new Date(),
      resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0015),
      errorCode: AppConstants.ERROR_CODES.EMET0015,
    });
    context.log(`[RunbookProcessorFunc] エクスポートファイルなし: taskId=${taskId}`);
    return;
  }

  // iii-iv. 各ファイルに対し、新しいBlobファイル名を構築し、Azure Blob Storageへコピーする
  const containerClient = createBlobServiceClient().getContainerClient(OPLOGS_CONTAINER);

  for (let i = 0; i < files.length; i++) {
    const sourceFileName = files[i];
    // iii. Task.taskName および元のファイル名の連番部分を使用し、新しいBlobファイル名を構築
    const sequenceNumber = sourceFileName.match(/exportoplog_(\d+)\.zip/)?.[1] || (i + 1).toString();
    const targetBlobName = `${task.licenseId}/${taskId}/${task.taskName}_${sequenceNumber}.zip`;

    // iv. 元のファイルを新しいBlobファイル名でAzure Blob Storageの最終保存場所へコピーする
    const sourceFileClient = exportsDir.getFileClient(sourceFileName);

    // 先下载再上传的方式，避免跨服务认证问题
    const downloadResponse = await sourceFileClient.downloadToBuffer();
    const blobClient = containerClient.getBlockBlobClient(targetBlobName);
    await blobClient.uploadData(downloadResponse);

    context.log(`[RunbookProcessorFunc] 操作ログファイルコピー完了: ${sourceFileName} -> ${targetBlobName}`);

    // vi. コピー成功後、操作ログOperationLogテーブルに新規レコードを作成
    const fileStats = await sourceFileClient.getProperties();
    operationLogsToCreate.push({
      name: `${task.taskName}_${sequenceNumber}.zip`,
      size: fileStats.contentLength || 0,
      createdAt: new Date(),
      retentionAt: null,
      licenseId: task.licenseId!,
      fileName: `${task.taskName}_${sequenceNumber}.zip`,
      generatedByTaskId: taskId,
    });
  }

  // viii. 操作ログレコード作成成功時
  Object.assign(taskUpdateData, {
    status: AppConstants.TaskStatus.CompletedSuccess,
    endedAt: new Date(),
    resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0014), // EMET0014は{}なしで使用
  });
  context.log(`[RunbookProcessorFunc] 操作ログエクスポート完了: taskId=${taskId}`);
}

/**
 * 管理項目定義エクスポート完了処理
 * assetsfield_def.csvファイルをAzure Blob Storageにコピー
 */
async function handleMgmtItemExportCompleted(
  task: Task,
  taskId: string,
  context: InvocationContext,
  taskUpdateData: Prisma.TaskUpdateInput
): Promise<void> {
  // i. Azure Filesワークスペースのエクスポートディレクトリ TaskWorkspaces/<taskId>/exports/ から、固定ファイル名 assetsfield_def.csv のファイルを取得する
  const shareClient = createShareServiceClient().getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
  const exportsDir = shareClient.getDirectoryClient(`${taskId}/exports`);
  const sourceFileClient = exportsDir.getFileClient("assetsfield_def.csv");

  const exists = await sourceFileClient.exists();

  // ii. 目標のファイルが存在しない場合
  if (!exists) {
    Object.assign(taskUpdateData, {
      status: AppConstants.TaskStatus.CompletedError,
      endedAt: new Date(),
      resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0015),
      errorCode: AppConstants.ERROR_CODES.EMET0015,
    });
    context.log(`[RunbookProcessorFunc] 管理項目定義ファイルなし: taskId=${taskId}`);
    return;
  }

  // iii. assetsfield_def.csv を Azure Blob Storage の最終保存場所へ、ファイル名はassetsfield_def.csvのままでコピーする
  const containerClient = createBlobServiceClient().getContainerClient(ASSETS_CONTAINER);
  const targetBlobName = `${task.licenseId}/exports/${taskId}/assetsfield_def.csv`;

  // 先下载再上传的方式，避免跨服务认证问题
  const downloadResponse = await sourceFileClient.downloadToBuffer();
  const blobClient = containerClient.getBlockBlobClient(targetBlobName);
  await blobClient.uploadData(downloadResponse);

  // v. コピー成功時
  Object.assign(taskUpdateData, {
    status: AppConstants.TaskStatus.CompletedSuccess,
    endedAt: new Date(),
  });

  context.log(`[RunbookProcessorFunc] 管理項目定義ファイルコピー完了: assetsfield_def.csv -> ${targetBlobName}`);
}

/**
 * automationJobStatus が "Failed" の場合の処理
 * errordetail.txtファイルの存在に応じてエラーメッセージを設定
 */
async function handleFailedStatus(
  task: Task,
  taskId: string,
  context: InvocationContext,
  taskUpdateData: Prisma.TaskUpdateInput,
  exception?: string
): Promise<void> {
  // i. Azure Files上のタスク用一時ワークスペースのエクスポートディレクトリ TaskWorkspaces/<taskId>/exports/ からエラーメッセージファイル(errordetail.txt)の存在を確認する
  const shareClient = createShareServiceClient().getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
  const exportsDir = shareClient.getDirectoryClient(`${taskId}/exports`);
  const errorFileClient = exportsDir.getFileClient("errordetail.txt");

  const errorFileExists = await errorFileClient.exists();

  if (errorFileExists) {
    // ii. errordetail.txt が存在している場合
    const downloadResponse = await errorFileClient.download();
    const errorDetail = await streamToString(downloadResponse.readableStreamBody!);

    Object.assign(taskUpdateData, {
      status: AppConstants.TaskStatus.CompletedError,
      endedAt: new Date(),
      resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0011, [errorDetail]),
      errorCode: AppConstants.ERROR_CODES.EMET0011,
      errorMessage: exception,
    });
    context.log(`[RunbookProcessorFunc] 基盤スクリプト実行エラー: taskId=${taskId}, detail=${errorDetail}`);
  } else {
    // iii. errordetail.txt が存在していない場合
    Object.assign(taskUpdateData, {
      status: AppConstants.TaskStatus.CompletedError,
      endedAt: new Date(),
      resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0012),
      errorCode: AppConstants.ERROR_CODES.EMET0012,
      errorMessage: exception,
    });
    context.log(`[RunbookProcessorFunc] 基盤スクリプト起動エラー: taskId=${taskId}`);
  }
}

/**
 * ストリームを文字列に変換するヘルパー関数
 */
async function streamToString(readableStream: NodeJS.ReadableStream): Promise<string> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks).toString("utf8"));
    });
    readableStream.on("error", reject);
  });
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("RunbookProcessorFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME%",
  handler: RunbookProcessorFunc,
});
