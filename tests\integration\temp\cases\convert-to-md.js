/**
 * 将从 Excel 复制的测试用例文本文件转换为 Markdown 格式
 * 
 * 文件结构分析：
 * - 第1行：表头（項番	試験観点	試験対象	試験手順	確認項目）
 * - 从第2行开始：测试用例数据，每个用例可能跨多行
 * - 用例之间通过項番（序号）来区分
 * 
 * 使用方法：
 * node convert-to-md.js input.txt output.md
 */

const fs = require('fs');
const path = require('path');

function convertToMarkdown(inputFile, outputFile) {
    try {
        // 读取输入文件，尝试不同编码
        let content;
        try {
            content = fs.readFileSync(inputFile, 'utf-8');
        } catch (error) {
            // 尝试其他编码
            content = fs.readFileSync(inputFile, 'utf16le');
        }

        const lines = content.split(/\r?\n/);
        
        if (lines.length < 2) {
            throw new Error('文件内容不足，至少需要表头和一行数据');
        }
        
        // 解析表头
        const headers = lines[0].split('\t');
        console.log('检测到的表头:', headers);
        
        // 解析测试用例数据
        const testCases = parseTestCases(lines.slice(1), headers.length);

        console.log(`解析完成，共找到 ${testCases.length} 个测试用例`);

        // 生成 Markdown 内容
        const markdown = generateMarkdown(headers, testCases);
        
        // 写入输出文件
        fs.writeFileSync(outputFile, markdown, 'utf-8');
        
        console.log(`✅ 转换完成！`);
        console.log(`📄 输入文件: ${inputFile}`);
        console.log(`📄 输出文件: ${outputFile}`);
        console.log(`📊 共转换 ${testCases.length} 个测试用例`);
        
    } catch (error) {
        console.error('❌ 转换失败:', error.message);
        process.exit(1);
    }
}

function parseTestCases(dataLines, expectedColumns) {
    const testCases = [];
    let currentCase = null;

    for (let i = 0; i < dataLines.length; i++) {
        const line = dataLines[i];
        if (!line || line.trim() === '') continue;

        const parts = line.split('\t');

        // 检查是否是新的测试用例（第一列是纯数字）
        if (parts[0] && /^\d+$/.test(parts[0].trim())) {
            // 保存上一个用例
            if (currentCase) {
                testCases.push(currentCase);
            }

            // 开始新用例
            currentCase = {
                id: parts[0].trim(),
                testPoint: parts[1] ? parts[1].trim() : '',
                testTarget: parts[2] ? parts[2].trim() : '',
                testSteps: parts[3] ? parts[3].trim() : '',
                verificationItems: parts[4] ? parts[4].trim() : ''
            };

            console.log(`解析测试用例 ${currentCase.id}: ${currentCase.testPoint.substring(0, 30)}...`);
        } else if (currentCase) {
            // 继续当前用例的内容 - 这些是跨行的内容
            for (let j = 0; j < parts.length && j < 5; j++) {
                const content = parts[j] ? parts[j].trim() : '';
                if (!content) continue;

                switch (j) {
                    case 0: // 項番列 - 通常为空，跳过
                        break;
                    case 1: // 試験観点
                        if (currentCase.testPoint) currentCase.testPoint += '\n' + content;
                        else currentCase.testPoint = content;
                        break;
                    case 2: // 試験対象
                        if (currentCase.testTarget) currentCase.testTarget += '\n' + content;
                        else currentCase.testTarget = content;
                        break;
                    case 3: // 試験手順
                        if (currentCase.testSteps) currentCase.testSteps += '\n' + content;
                        else currentCase.testSteps = content;
                        break;
                    case 4: // 確認項目
                        if (currentCase.verificationItems) currentCase.verificationItems += '\n' + content;
                        else currentCase.verificationItems = content;
                        break;
                }
            }
        }
    }

    // 添加最后一个用例
    if (currentCase) {
        testCases.push(currentCase);
    }

    return testCases;
}

function generateMarkdown(headers, testCases) {
    let markdown = `# 测试用例文档\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    
    // 生成目录
    markdown += `## 目录\n\n`;
    testCases.forEach(testCase => {
        const title = testCase.testPoint.split('\n')[0].substring(0, 50);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${cleanContent(testCase.testPoint)}\n\n`;
        
        markdown += `### 试验对象\n`;
        markdown += formatList(testCase.testTarget) + '\n\n';
        
        markdown += `### 试验手順\n`;
        markdown += formatSteps(testCase.testSteps) + '\n\n';
        
        markdown += `### 确认项目\n`;
        markdown += formatSteps(testCase.verificationItems) + '\n\n';
        
        markdown += `---\n\n`;
    });
    
    return markdown;
}

function cleanContent(content) {
    return content.replace(/^\n+|\n+$/g, '').trim();
}

function formatList(content) {
    const cleaned = cleanContent(content);
    if (!cleaned) return '';
    
    const lines = cleaned.split('\n');
    return lines.map(line => {
        const trimmed = line.trim();
        if (!trimmed) return '';
        if (trimmed.startsWith('-') || trimmed.startsWith('*')) {
            return trimmed;
        }
        return `- ${trimmed}`;
    }).filter(line => line).join('\n');
}

function formatSteps(content) {
    const cleaned = cleanContent(content);
    if (!cleaned) return '';
    
    const lines = cleaned.split('\n');
    let result = [];
    
    lines.forEach(line => {
        const trimmed = line.trim();
        if (!trimmed) return;
        
        // 检查是否已经是编号格式
        if (trimmed.match(/^\d+\./)) {
            result.push(trimmed);
        } else {
            result.push(trimmed);
        }
    });
    
    return result.join('\n\n');
}

// 主程序
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length < 1) {
        console.log('使用方法: node convert-to-md.js <input.txt> [output.md]');
        console.log('示例: node convert-to-md.js 2-4-1.txt 2-4-1.md');
        process.exit(1);
    }
    
    const inputFile = args[0];
    const outputFile = args[1] || inputFile.replace(/\.[^.]+$/, '.md');
    
    if (!fs.existsSync(inputFile)) {
        console.error(`❌ 输入文件不存在: ${inputFile}`);
        process.exit(1);
    }
    
    convertToMarkdown(inputFile, outputFile);
}

module.exports = { convertToMarkdown };
