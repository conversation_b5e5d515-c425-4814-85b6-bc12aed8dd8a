/**
 * @fileoverview タスク一覧ページに表示されるタスクデータのテーブルコンポーネント
 * @description
 * タスク一覧ページ（/dashboard/tasks）に表示されるタスクデータのテーブルを構築する。
 * 親コンポーネントから渡されるフィルタリング、ページネーション、ソートの状態に基づき、
 * ServerDataモジュールを介して適切なタスクデータを非同期に取得し、テーブル形式でレンダリングする。
 * 各タスク行には、タスク名、ステータス、日時情報などが含まれる。
 * 特に「タスク詳細」列は、タスクの現在のステータスに応じて動的なアクション（中止ボタン、ダウンロードリンク、エラー詳細表示など）を提供する。
 *
 * 主な機能:
 * - URLパラメータに連動した、フィルタ・ソート・ページング済みのタスク一覧の表示
 * - ユーザーのタイムゾーンに基づいた日時のフォーマット
 * - タスクのステータスと種別に応じた、インタラクティブなアクション項目の動的レンダリング
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ServerDataTasks } from "@/app/lib/data/tasks";
import Thead from "../thead";
import { formatDate } from "@/app/lib/utils";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import TaskActions from "@/app/ui/tasks/actions-modals";

/**
 * タスクテーブルコンポーネント
 *
 * コンポーネントのプロパティに基づいてタスク一覧をフィルタリング、ソート、ページングして表示する
 * @param props - コンポーネントのプロパティ
 * @param props.filter - タスク名による検索フィルタ文字列
 * @param props.page - 表示するページ番号（1から開始）
 * @param props.size - 1ページあたりの表示件数
 * @param props.sort - ソート対象のカラム名
 * @param props.order - ソート順序（昇順または降順）
 * @returns フィルタリング、ソート、ページングされたタスク一覧テーブル
 */
export default async function TasksTable({
  filter,
  page,
  size,
  sort,
  order,
}: {
  filter: string;
  page: number;
  size: number;
  sort:
    | "taskName"
    | "status"
    | "startedAt"
    | "endedAt"
    | "targetServerName"
    | "taskType"
    | "submittedByUserId";
  order: "asc" | "desc";
}) {
  // 1. フィルタリング、ページネーション、ソートされたタスクデータの取得
  // 親コンポーネントから渡されたパラメータに基づき、ServerDataモジュールを呼び出し、
  // 表示に必要なタスクデータを非同期に取得します。
  const tasks = await ServerDataTasks.fetchFilteredTasks(
    filter,
    size,
    page,
    sort,
    order,
  );

  // 2. ユーザーセッションからタイムゾーンの取得
  // 日時フィールド（開始日時、終了日時）をユーザーのローカルタイムゾーンで表示するため、
  // iron-sessionを介してセッションデータからタイムゾーン情報を取得します。
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  const userTimezone = session!.user.tz;

  // テーブルヘッダーのレンダリング：Theadコンポーネントを利用してソート可能なテーブルヘッダーを生成
  return (
    <table className="whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "taskName", label: "タスク名" },
          { key: "status", label: "ステータス" },
          { key: "startedAt", label: "開始日時" },
          { key: "endedAt", label: "終了日時" },
          { key: "targetServerName", label: "サーバ名" },
          { key: "taskType", label: "タスク種別" },
          { key: "submittedByUserId", label: "実行ユーザー" },
          { key: "actions", label: "タスク詳細", sortable: false },
        ]}
        defaultOrder="startedAt"
        defaultSort="desc"
      />
      <tbody>
        {tasks && tasks.length > 0 ? (
          tasks.map((task) => (
            <tr key={task.id} className="border-b odd:bg-white even:bg-gray-50">
              <td className="border-r px-6 py-4">{task.taskName}</td>
              <td className="border-r px-6 py-4">{task.statusLabel}</td>
              <td className="border-r px-6 py-4">
                {task.startedAt ? formatDate(task.startedAt, userTimezone) : ""}
              </td>
              <td className="border-r px-6 py-4">
                {task.endedAt ? formatDate(task.endedAt, userTimezone) : ""}
              </td>
              <td className="border-r px-6 py-4">{task.targetServerName}</td>
              <td className="border-r px-6 py-4">{task.taskTypeLabel}</td>
              <td className="border-r px-6 py-4">{task.submittedByUserId}</td>
              <td className="px-6 py-4">
                <TaskActions task={task} />
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={8} className="text-center py-4 text-gray-500">
              <div className="p-4">該当するタスクがありません</div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
