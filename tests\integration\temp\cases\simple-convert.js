/**
 * 简化版本的测试用例转换脚本
 */

const fs = require('fs');

function convertToMarkdown(inputFile, outputFile) {
    console.log('开始转换...');
    
    // 读取文件
    const content = fs.readFileSync(inputFile, 'utf-8');
    const lines = content.split(/\r?\n/);
    
    console.log(`读取到 ${lines.length} 行数据`);
    
    // 解析表头
    const headers = lines[0].split('\t');
    console.log('表头:', headers);
    
    // 解析测试用例
    const testCases = [];
    let currentCase = null;
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        if (!line || line.trim() === '') continue;
        
        const parts = line.split('\t');
        
        // 检查是否是新的测试用例（第一列是数字）
        if (parts[0] && /^\d+$/.test(parts[0].trim())) {
            // 保存上一个用例
            if (currentCase) {
                testCases.push(currentCase);
            }
            
            // 开始新用例
            currentCase = {
                id: parts[0].trim(),
                testPoint: parts[1] ? parts[1].trim() : '',
                testTarget: parts[2] ? parts[2].trim() : '',
                testSteps: parts[3] ? parts[3].trim() : '',
                verificationItems: parts[4] ? parts[4].trim() : ''
            };
            
            console.log(`找到测试用例 ${currentCase.id}`);
        } else if (currentCase && parts.length > 0) {
            // 继续当前用例的内容
            for (let j = 1; j < Math.min(parts.length, 5); j++) {
                const content = parts[j] ? parts[j].trim() : '';
                if (!content) continue;
                
                switch (j) {
                    case 1: // 試験観点
                        currentCase.testPoint += (currentCase.testPoint ? '\n' : '') + content;
                        break;
                    case 2: // 試験対象
                        currentCase.testTarget += (currentCase.testTarget ? '\n' : '') + content;
                        break;
                    case 3: // 試験手順
                        currentCase.testSteps += (currentCase.testSteps ? '\n' : '') + content;
                        break;
                    case 4: // 確認項目
                        currentCase.verificationItems += (currentCase.verificationItems ? '\n' : '') + content;
                        break;
                }
            }
        }
    }
    
    // 添加最后一个用例
    if (currentCase) {
        testCases.push(currentCase);
    }
    
    console.log(`解析完成，共 ${testCases.length} 个测试用例`);
    
    // 生成 Markdown
    let markdown = `# 测试用例文档 (2-4-1)\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    markdown += `## 目录\n\n`;
    
    // 生成目录
    testCases.forEach(testCase => {
        const title = testCase.testPoint.split('\n')[0].substring(0, 60);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${testCase.testPoint}\n\n`;
        
        markdown += `### 试验对象\n`;
        const targets = testCase.testTarget.split('\n').filter(t => t.trim());
        targets.forEach(target => {
            markdown += `- ${target}\n`;
        });
        markdown += `\n`;
        
        markdown += `### 试验手順\n`;
        markdown += `${testCase.testSteps}\n\n`;
        
        markdown += `### 确认项目\n`;
        markdown += `${testCase.verificationItems}\n\n`;
        
        markdown += `---\n\n`;
    });
    
    // 写入文件
    fs.writeFileSync(outputFile, markdown, 'utf-8');
    
    console.log(`✅ 转换完成！输出文件: ${outputFile}`);
}

// 运行转换
if (require.main === module) {
    const inputFile = process.argv[2] || '2-4-1.txt';
    const outputFile = process.argv[3] || '2-4-1.md';
    
    convertToMarkdown(inputFile, outputFile);
}
