/**
 * @fileoverview 管理项目导出完整流程测试 - 最终版本
 * @description
 * 使用修复后的服务管理器，实现完整的端到端测试：
 * 1. 在 beforeAll 中启动完整服务栈
 * 2. 执行前端导出操作
 * 3. 使用 expect.poll 轮询验证任务状态变化
 * 4. 验证 Azure Functions 日志和 Mock Server 调用
 * 5. 支持日志断言和数据库断言
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { test, expect } from '@playwright/test';
import { loginAs, logout, UserRole } from '../support/auth.helper';
import {
  ServerType,
  TestServerConfig,
  cleanupAllTestServers,
  forceCleanupAllTestServers,
  createTestServer,
  createTestLicense,
  disconnectPrisma,
  prisma,
  generateTestLicenseId
} from '../support/server-data.helper';
import {
  interceptRefreshTokenRequests,
  clearNetworkInterceptors
} from '../support/network-interceptor.helper';
import { MockServerHelper } from '../support/mock-server-helper';
import { MockJobStatus } from '../support/azure-automation-mock-server';
import { TestServicesManager } from '../support/test-services-manager';

// 全局服务管理器
let servicesManager: TestServicesManager;
let mockServerHelper: MockServerHelper;

// 测试许可证ID
let TEST_LICENSE_ID: string;

// 测试服务器配置
let testServer: TestServerConfig;

/**
 * 关闭所有可能打开的模态框
 */
async function closeAllModals(page: any) {
  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      const closeButtons = await page.locator('button:has-text("Close modal")').all();
      for (const button of closeButtons) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(300);
        }
      }
    } catch (error) {
      // 忽略错误
    }

    try {
      const cancelButtons = await page.locator('button:has-text("キャンセル")').all();
      for (const button of cancelButtons) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(300);
        }
      }
    } catch (error) {
      // 忽略错误
    }

    try {
      await page.keyboard.press('Escape');
      await page.waitForTimeout(300);
    } catch (error) {
      // 忽略错误
    }

    const remainingModals = await page.locator('[role="dialog"], .modal, [data-modal]').count();
    if (remainingModals === 0) {
      break;
    }
  }
}

test.describe('管理项目导出完整流程测试 - 最终版本', () => {
  
  // 在所有测试开始前启动完整服务栈
  test.beforeAll(async () => {
    console.log('🚀 开始启动完整测试环境...');
    
    // 生成测试许可证ID
    TEST_LICENSE_ID = generateTestLicenseId();
    console.log(`📝 测试许可证ID: ${TEST_LICENSE_ID}`);
    
    // 创建测试许可证
    await createTestLicense(TEST_LICENSE_ID);
    
    // 设置 Azure Files 连接字符串环境变量
    if (!process.env.AZURE_STORAGE_FILES_CONNECTION_STRING) {
      process.env.AZURE_STORAGE_FILES_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net';
      console.log('🔧 设置 Azure Files 连接字符串环境变量');
    }

    // 设置 Azure Blob Storage 环境变量（使用线上服务）
    if (!process.env.AZURE_STORAGE_BLOB_CONNECTION_STRING) {
      process.env.AZURE_STORAGE_BLOB_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net';
      console.log('🔧 设置 Azure Blob Storage 连接字符串环境变量（线上服务）');
    }

    // 初始化服务管理器
    servicesManager = new TestServicesManager();
    mockServerHelper = servicesManager.getMockServerHelper();

    try {
      // 按顺序启动服务（使用智能Azurite管理）
      console.log('🔧 Step 1: 确保 Azurite 运行...');
      await servicesManager.ensureAzuriteRunning();
      console.log('✅ Azurite 已就绪');
      
      console.log('🔧 Step 2: 启动 Mock Server...');
      await servicesManager.startMockServer();
      console.log('✅ Mock Server 启动成功');
      
      console.log('🔧 Step 3: 启动标准 Azure Functions...');
      await servicesManager.startStandardFunctions();
      console.log('✅ 标准 Azure Functions 启动成功');
      
      console.log('🔧 Step 4: 启动长时间运行 Azure Functions...');
      await servicesManager.startLongRunningFunctions();
      console.log('✅ 长时间运行 Azure Functions 启动成功');
      
      // 验证所有服务状态
      console.log('📊 服务状态验证:');
      console.log(`   - Azurite: ${servicesManager.isServiceRunning('azurite') ? '✅ 运行中' : '❌ 未运行'}`);
      console.log(`   - Mock Server: ${mockServerHelper.isRunning() ? '✅ 运行中' : '❌ 未运行'}`);
      console.log(`   - Standard Functions: ${servicesManager.isServiceRunning('standard-functions') ? '✅ 运行中' : '❌ 未运行'}`);
      console.log(`   - Long-Running Functions: ${servicesManager.isServiceRunning('long-running-functions') ? '✅ 运行中' : '❌ 未运行'}`);

      // 创建必要的 Azure Blob 容器（线上服务）
      console.log('🔧 Step 5: 创建 Azure Blob 容器...');
      await createBlobContainers();
      console.log('✅ Azure Blob 容器创建完成');

      console.log('✅ 完整测试环境启动成功');
      
    } catch (error) {
      console.error('❌ 启动测试环境失败:', error);
      throw error;
    }
  });
  
  // 在所有测试结束后停止服务
  test.afterAll(async () => {
    console.log('🛑 开始停止测试环境...');
    
    try {
      // 清理测试数据
      await comprehensiveTestDataCleanup(TEST_LICENSE_ID);
      
      // 停止所有服务
      if (servicesManager) {
        await servicesManager.stopAllServices();
      }
      
      // 断开数据库连接
      await disconnectPrisma();
      
      console.log('✅ 测试环境清理完成');
    } catch (error) {
      console.error('⚠️ 清理测试环境时出错:', error);
    }
  });

  // 在每个测试开始前清理数据
  test.beforeEach(async ({ page }) => {
    // 设置网络拦截
    await interceptRefreshTokenRequests(page);
    
    // 清理测试数据
    await comprehensiveTestDataCleanup(TEST_LICENSE_ID);
    
    // 清理Mock Server状态
    await mockServerHelper.clearAllJobs();
    await mockServerHelper.clearFailureScenarios();
    
    // 创建测试服务器
    testServer = {
      name: `test-export-server-${Date.now()}`,
      type: ServerType.GENERAL_MANAGER,
      url: 'https://example.com/test-server',
      licenseId: TEST_LICENSE_ID,
      azureVmName: 'test-vm-export',
      dockerContainerName: 'test-container-export',
      hrwGroupName: 'test-hrw-group-export'
    };
    
    await createTestServer(testServer);
    console.log(`✅ 已创建测试服务器: ${testServer.name}`);
  });
  
  // 在每个测试结束后清理
  test.afterEach(async ({ page }) => {
    // 清理网络拦截
    await clearNetworkInterceptors(page);
    
    // 清理测试数据
    await comprehensiveTestDataCleanup(TEST_LICENSE_ID);
    
    // 登出
    await logout(page);
  });

  test('应该完成完整的管理项目导出流程', async ({ page }) => {
    console.log('🔍 开始完整的管理项目导出流程测试');
    
    // 步骤1: 登录系统
    console.log('📝 步骤1: 用户登录');
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    
    // 步骤2: 访问服务器列表页面
    console.log('📝 步骤2: 访问服务器列表');
    await page.goto('/dashboard/servers');
    await page.waitForLoadState('networkidle');
    
    // 验证页面加载成功
    await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
    await expect(page.getByText(testServer.name as string)).toBeVisible();
    console.log('✅ 服务器列表页面加载成功');

    
    // 步骤3: 执行导出操作
    console.log('📝 步骤3: 执行管理项目导出');
    const serverRow = page.locator(`tr:has-text("${testServer.name}")`);
    const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');
    
    await expect(taskSelectButton).toBeVisible();
    await taskSelectButton.click();
    
    const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
    await expect(exportMenuItem).toBeVisible();
    await exportMenuItem.click();
    
    // 确认对话框
    const confirmDialog = page.locator('[role="dialog"], .modal, [data-modal]');
    await expect(confirmDialog).toBeVisible();
    
    const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行")');
    await expect(confirmButton.first()).toBeVisible();
    await confirmButton.first().click();

    
    // 验证成功消息
    const successMessage = page.locator('text=/タスクの実行を受け付けました/');
    await expect(successMessage).toBeVisible();
    console.log('✅ 导出任务创建成功');

    
    // 步骤4: 验证任务在数据库中创建
    console.log('📝 步骤4: 验证任务数据库记录');
    await page.waitForTimeout(2000);
    
    const createdTask = await prisma.task.findFirst({
      where: {
        taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
        licenseId: TEST_LICENSE_ID,
        targetServerName: testServer.name
      },
      orderBy: { submittedAt: 'desc' }
    });
    
    expect(createdTask).toBeTruthy();
    console.log(`✅ 任务已创建: ${createdTask!.id}`);
    console.log(`📊 初始状态: ${createdTask!.status}`);

    
    // 步骤5: 使用 expect.poll 轮询验证任务状态变化
    console.log('📝 步骤5: 轮询验证任务状态变化');
    
    // 轮询验证任务状态变化 - 记录缺陷
    let finalTaskStatus: string | undefined;
    let taskErrorDetails: any = {};
    let defectAlreadyLogged = false; // 标记是否已经记录过缺陷信息
    let jobStatusSet = false; // 标记是否已经设置了作业状态

    await expect.poll(async () => {
      const task = await prisma.task.findUnique({
        where: { id: createdTask!.id }
      });
      console.log(`📊 当前任务状态: ${task?.status}`);

      finalTaskStatus = task?.status;

      // 当任务状态变为 RUNBOOK_SUBMITTED 时，等待自动状态变化完成后设置模拟作业状态为 Completed
      if (task?.status === 'TASK_STATUS.RUNBOOK_SUBMITTED' && !jobStatusSet) {
        try {
          // 等待 1 秒，确保自动状态变化（New -> Activating -> Running）完成
          console.log(`⏳ 等待自动状态变化完成...`);
          await new Promise(resolve => setTimeout(resolve, 1000));

          await mockServerHelper.setJobStatus(task.id, MockJobStatus.Completed);
          console.log(`✅ 已设置作业状态为 Completed: ${task.id}`);
          jobStatusSet = true;
        } catch (error) {
          console.warn(`⚠️ 设置作业状态失败:`, error);
        }
      }

      // 如果任务失败，记录错误信息用于缺陷报告（只记录一次）
      if (task?.status === 'TASK_STATUS.COMPLETED_ERROR' && !defectAlreadyLogged) {
        taskErrorDetails = {
          errorCode: task.errorCode,
          errorMessage: task.errorMessage,
          resultMessage: task.resultMessage,
          taskName: task.taskName
        };
        console.log(`❌ 发现缺陷 - 任务失败详情:`);
        console.log(`   错误代码: ${task.errorCode}`);
        console.log(`   错误消息: ${task.errorMessage}`);
        console.log(`   结果消息: ${task.resultMessage}`);
        console.log(`   任务名称: ${task.taskName}`);

        defectAlreadyLogged = true; // 标记已记录，避免重复打印
      }

      // 如果任务已完成（成功或失败），停止轮询
      if (task?.status === 'TASK_STATUS.COMPLETED_SUCCESS' ||
          task?.status === 'TASK_STATUS.COMPLETED_ERROR') {
        return task.status;
      }

      return task?.status;
    }, {
      message: '等待任务完成（包括 RunbookMonitorFunc 处理）',
      timeout: 90000, // 增加超时时间，等待 RunbookMonitorFunc 处理（30秒间隔 + 处理时间）
      intervals: [5000, 10000, 15000] // 增加轮询间隔
    }).toBe('TASK_STATUS.COMPLETED_SUCCESS'); // 等待任务成功完成
    
    // 验证完整流程必须成功完成
    if (finalTaskStatus !== 'TASK_STATUS.COMPLETED_SUCCESS') {
      console.log('❌ 管理项目导出流程失败');
      console.log('📋 失败详情已记录，可供开发团队分析');
      
      // 测试失败 - 因为完整流程没有成功
      throw new Error(`管理项目导出流程失败，任务状态: ${finalTaskStatus}`);
    }
    
    console.log('✅ 管理项目导出流程成功完成');
    expect(finalTaskStatus).toBe('TASK_STATUS.COMPLETED_SUCCESS');
    
    // 步骤6: 验证 Azure Functions 处理了任务
    console.log('📝 步骤6: 验证 Azure Functions 处理了任务');
    
    const standardFunctionsLogs = servicesManager.getServiceLogs('standard-functions');
    const longRunningFunctionsLogs = servicesManager.getServiceLogs('long-running-functions');
    
    // 验证日志中包含任务处理相关信息
    const allLogs = [...standardFunctionsLogs, ...longRunningFunctionsLogs].join('\n');
    
    expect(allLogs).toContain(createdTask!.id);
    console.log('✅ Azure Functions 处理了任务');
    
    console.log('📋 完整的管理项目导出流程测试完成');

  });

  test('应该验证服务状态和日志断言', async () => {
    console.log('🔍 验证服务状态和日志断言');
    
    // 验证所有服务都在运行
    expect(servicesManager.isServiceRunning('azurite')).toBeTruthy();
    expect(mockServerHelper.isRunning()).toBeTruthy();
    expect(servicesManager.isServiceRunning('standard-functions')).toBeTruthy();
    expect(servicesManager.isServiceRunning('long-running-functions')).toBeTruthy();
    
    console.log('✅ 所有服务状态验证通过');
    
    // 验证 Azure Functions 日志包含预期内容
    const standardLogs = servicesManager.getServiceLogs('standard-functions');
    const longRunningLogs = servicesManager.getServiceLogs('long-running-functions');
    
    const standardLogsText = standardLogs.join('\n');
    const longRunningLogsText = longRunningLogs.join('\n');
    
    // 日志断言 - 验证标准 Functions 包含预期的函数
    expect(standardLogsText).toContain('RunbookMonitorFunc');
    expect(standardLogsText).toContain('TaskExecuteFunc');
    expect(standardLogsText).toContain('Worker process started');
    
    // 日志断言 - 验证长时间运行 Functions 包含预期的函数
    expect(longRunningLogsText).toContain('RunbookProcessorFunc');
    expect(longRunningLogsText).toContain('Worker process started');
    
    console.log('✅ Azure Functions 日志断言验证通过');
    
    // 验证 Mock Server 健康状态
    const healthResponse = await fetch('http://localhost:3001/health');
    expect(healthResponse.ok).toBeTruthy();
    
    console.log('✅ Mock Server 健康检查通过');
  });
});

/**
 * 全面清理测试数据
 */
async function comprehensiveTestDataCleanup(licenseId: string) {
  try {
    console.log(`🧹 开始清理测试数据 (许可证: ${licenseId})`);

    // 清理任务记录
    const deletedTasks = await prisma.task.deleteMany({
      where: {
        OR: [
          { licenseId: licenseId },
          { id: { startsWith: 'test-' } },
          { taskName: { contains: 'test' } }
        ]
      }
    });
    console.log(`✅ 清理了 ${deletedTasks.count} 个任务记录`);

    // 清理容器状态记录
    const deletedContainerStatus = await prisma.containerConcurrencyStatus.deleteMany({
      where: {
        OR: [
          { targetVmName: { startsWith: 'test-' } },
          { targetContainerName: { startsWith: 'test-' } },
          { currentTaskId: { startsWith: 'test-' } }
        ]
      }
    });
    console.log(`✅ 清理了 ${deletedContainerStatus.count} 个容器状态记录`);

    // 清理服务器记录
    const deletedServers = await prisma.server.deleteMany({
      where: { licenseId: licenseId }
    });
    console.log(`✅ 清理了 ${deletedServers.count} 个服务器记录`);

    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`✅ 测试数据清理完成`);
  } catch (error) {
    console.error(`❌ 清理测试数据时出错:`, error);
  }
}

/**
 * 创建必要的 Azure Blob 容器（线上服务）
 */
async function createBlobContainers() {
  try {
    const { BlobServiceClient } = await import('@azure/storage-blob');

    // 使用线上 Azure Blob Storage 连接字符串
    const connectionString = 'DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net';
    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);

    // 创建必要的容器
    const containers = [
      'assetsfield-def-test',  // 管理项目定义文件容器
      'oplogs-test'            // 操作日志文件容器
    ];

    for (const containerName of containers) {
      const containerClient = blobServiceClient.getContainerClient(containerName);
      const exists = await containerClient.exists();

      if (!exists) {
        await containerClient.create();
        console.log(`✅ 创建 Blob 容器: ${containerName}`);
      } else {
        console.log(`ℹ️ Blob 容器已存在: ${containerName}`);
      }
    }
  } catch (error) {
    console.warn('⚠️ 创建 Blob 容器失败:', error);
    // 不抛出异常，允许测试继续进行
  }
}

/**
 * 安全地清理单个任务
 */
async function safeCleanupTask(taskId: string) {
  try {
    await prisma.task.delete({ where: { id: taskId } });
    console.log(`✅ 已清理测试任务: ${taskId}`);
  } catch (error) {
    console.log(`⚠️ 清理测试任务失败: ${taskId}`, error instanceof Error ? error.message : String(error));
  }
}