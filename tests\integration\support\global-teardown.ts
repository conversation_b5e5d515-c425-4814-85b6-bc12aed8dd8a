import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

/**
 * 🧹 全局测试环境清理 - 业界最佳实践
 *
 * 功能：
 * - 进程和端口清理
 * - 数据库清理
 * - 测试数据清理
 * - 性能报告生成
 * - 资源释放
 * - 测试总结报告
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始全局测试环境清理...');

  try {
    // 0. 🔧 强制清理所有测试相关进程和端口
    await forceCleanupTestProcesses();

    // 1. 🗄️ 数据库清理
    await cleanupDatabase();

    // 2. 📊 生成测试总结报告
    await generateTestSummary();

    // 3. 📈 生成性能报告
    await generatePerformanceReport();

    // 4. 🧹 清理临时文件
    await cleanupTempFiles();

    console.log('✅ 全局测试环境清理完成');

  } catch (error) {
    console.error('❌ 全局清理失败:', error);
    // 不抛出错误，避免影响测试结果
  }
}

/**
 * 🔧 强制清理所有测试相关进程和端口
 */
async function forceCleanupTestProcesses() {
  console.log('🔧 强制清理测试进程和端口...');

  // 注意：排除所有 Azurite 端口 (10000, 10001, 10002)，因为使用手动启动的 Azurite 以提高稳定性
  // 10000: Azurite Blob service
  // 10001: Azurite Queue service
  // 10002: Azurite Table service
  const testPorts = [3000, 3001, 7071, 7072];

  for (const port of testPorts) {
    try {
      console.log(`🔍 检查端口 ${port}...`);
      const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });

      if (result.trim()) {
        // 提取 PID
        const lines = result.trim().split('\n');
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5) {
            const pid = parts[4];
            if (pid && pid !== '0' && !isNaN(Number(pid))) {
              try {
                console.log(`🔚 终止进程 PID ${pid} (端口 ${port})`);
                // 先检查进程是否仍然存在
                try {
                  execSync(`tasklist /FI "PID eq ${pid}"`, { stdio: 'pipe' });
                  // 进程存在，尝试终止
                  execSync(`taskkill /PID ${pid} /F`, { stdio: 'pipe' });
                  console.log(`✅ 成功终止进程 ${pid}`);
                } catch (checkError) {
                  // 进程可能已经不存在了
                  console.log(`ℹ️ 进程 ${pid} 可能已经终止`);
                }
              } catch (killError) {
                console.log(`⚠️ 无法终止进程 ${pid} (可能已经终止)`);
              }
            }
          }
        }
      } else {
        console.log(`✅ 端口 ${port} 未被占用`);
      }
    } catch (error) {
      // 端口未被占用或其他错误，继续处理下一个端口
      console.log(`✅ 端口 ${port} 未被占用`);
    }
  }

  // 额外等待确保进程完全终止
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('✅ 测试进程和端口清理完成');
}

/**
 * 🗄️ 数据库清理 - 清理测试数据
 */
async function cleanupDatabase() {
  console.log('🗄️ 清理测试数据库...');
  
  try {
    // 这里可以添加数据库清理逻辑
    // 例如：删除测试数据、重置序列等
    
    console.log('✅ 数据库清理完成');
  } catch (error) {
    console.error('❌ 数据库清理失败:', error);
  }
}

/**
 * 📊 生成测试总结报告
 */
async function generateTestSummary() {
  console.log('📊 生成测试总结报告...');
  
  try {
    const testResultsDir = path.resolve(__dirname, '../../test-results');
    const summaryPath = path.join(testResultsDir, 'test-summary.json');
    
    // 读取测试结果
    const testResultsPath = path.join(testResultsDir, 'test-results.json');
    let testResults = null;
    
    if (fs.existsSync(testResultsPath)) {
      testResults = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));
    }
    
    // 生成总结报告
    const summary = {
      timestamp: new Date().toISOString(),
      environment: 'test',
      results: testResults ? {
        total: testResults.stats?.total || 0,
        passed: testResults.stats?.passed || 0,
        failed: testResults.stats?.failed || 0,
        skipped: testResults.stats?.skipped || 0,
        duration: testResults.stats?.duration || 0,
      } : null,
      services: [
        'jcs-endpoint-nextjs',
        'jcs-backend-services-standard', 
        'jcs-backend-services-long-running'
      ],
      artifacts: {
        htmlReport: '../test-results/html-report/index.html',
        junitReport: '../test-results/junit-report.xml',
        traces: '../test-results/test-output/',
        har: '../test-results/har/',
      }
    };
    
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    
    console.log('✅ 测试总结报告生成完成');
    console.log(`📄 报告位置: ${summaryPath}`);
    
  } catch (error) {
    console.error('❌ 测试总结报告生成失败:', error);
  }
}

/**
 * 📈 生成性能报告
 */
async function generatePerformanceReport() {
  console.log('📈 生成性能报告...');
  
  try {
    const monitoringDir = path.resolve(__dirname, '../../test-results/monitoring');
    const sessionPath = path.join(monitoringDir, 'test-session.json');
    
    if (fs.existsSync(sessionPath)) {
      const session = JSON.parse(fs.readFileSync(sessionPath, 'utf8'));
      
      const performanceReport = {
        ...session,
        endTime: new Date().toISOString(),
        duration: Date.now() - new Date(session.startTime).getTime(),
        metrics: {
          // 这里可以添加性能指标收集
          // 例如：响应时间、内存使用、CPU 使用等
        }
      };
      
      const reportPath = path.join(monitoringDir, 'performance-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(performanceReport, null, 2));
      
      console.log('✅ 性能报告生成完成');
      console.log(`📊 报告位置: ${reportPath}`);
    }
    
  } catch (error) {
    console.error('❌ 性能报告生成失败:', error);
  }
}

/**
 * 🧹 清理临时文件
 */
async function cleanupTempFiles() {
  console.log('🧹 清理临时文件...');
  
  try {
    // 清理过期的追踪文件（保留最近的 10 个）
    const testOutputDir = path.resolve(__dirname, '../../test-results/test-output');
    if (fs.existsSync(testOutputDir)) {
      const files = fs.readdirSync(testOutputDir)
        .map(file => ({
          name: file,
          path: path.join(testOutputDir, file),
          mtime: fs.statSync(path.join(testOutputDir, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
      
      // 保留最新的 10 个文件，删除其余的
      const filesToDelete = files.slice(10);
      for (const file of filesToDelete) {
        try {
          if (fs.statSync(file.path).isDirectory()) {
            fs.rmSync(file.path, { recursive: true });
          } else {
            fs.unlinkSync(file.path);
          }
        } catch (error) {
          console.warn(`无法删除文件 ${file.path}:`, error);
        }
      }
      
      if (filesToDelete.length > 0) {
        console.log(`🗑️ 清理了 ${filesToDelete.length} 个过期文件`);
      }
    }
    
    console.log('✅ 临时文件清理完成');
    
  } catch (error) {
    console.error('❌ 临时文件清理失败:', error);
  }
}

export default globalTeardown;
