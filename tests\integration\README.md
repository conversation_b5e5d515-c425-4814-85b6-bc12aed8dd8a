# JCS 端点管理系统 E2E 测试

> **简洁的集成测试系统** - 基于 Playwright 的现代化 E2E 测试框架

## � 两种运行模式一览表

| 模式 | 命令 | webServer | 服务启动方式 | Next.js 模式 | 适用场景 |
|------|------|-----------|-------------|-------------|----------|
| **自动模式** | `npm test` | ✅ 启用 | Playwright 自动启动 | 🏭 **生产模式** | CI/CD、一键测试 |
| **调试模式** | `npm run test:debug` | 🔧 部分启用 | 自动启动 Next.js，测试中启动其他服务 | 🏭 **生产模式** | 开发调试、稳定性优先 |

## 🚀 快速开始

### 🖥️ 终端环境要求（重要前提）

**本项目必须在 Bash 环境中运行**，因为：
- 启动脚本 `scripts/start-local-env.sh` 是 Bash 脚本
- `package.json` 中使用了 `&&` 等 Bash 语法

#### Windows 用户推荐的 Bash 环境：
1. **Git Bash**（推荐）- 安装 Git for Windows 时自带
2. **WSL (Windows Subsystem for Linux)**（推荐）
3. **MSYS2** 或 **Cygwin**

#### ❌ 不支持的终端：
- **PowerShell** - 无法执行 Bash 脚本
- **Windows CMD** - 语法不兼容

### 前提条件
1. **确保应用已构建**：
   ```bash
   # Next.js 应用
   cd ../../apps/jcs-endpoint-nextjs && npm run build
   
   # Azure Functions
   cd ../../apps/jcs-backend-services-standard && npm run build
   cd ../../apps/jcs-backend-services-long-running && npm run build
   ```

### 方式一：自动启动服务（webServer 模式）
```bash
cd tests/integration

# 运行所有测试（自动启动所有服务）
npm test

# 运行单个测试文件（自动启动服务）
npm test specs/server-list.spec.ts

# 调试模式（自动启动服务）
npm run test:debug

# 调试特定文件（自动启动服务）
npm run test:debug specs/server-list.spec.ts

# 有头模式（自动启动服务）
npm run test:headed
```
**说明**：使用 `playwright.config.ts`，Playwright 会自动构建并启动完整服务栈（Next.js **生产模式**、Azure Functions、Mock Server、Azurite），无需手动操作。
⚠️ **注意**：首次运行需要构建时间，但测试环境更接近生产环境，符合 Playwright 最佳实践。

### 方式二：调试模式（推荐开发使用）
```bash
cd tests/integration

# 运行所有测试（自动启动 Next.js，测试中启动其他服务）
npm run test:debug

# 运行单个测试文件
npm run test:debug specs/server-list.spec.ts

# 调试模式
npm run test:debug -- --debug

# 调试特定文件
npm run test:debug -- --debug specs/server-list.spec.ts

# 有头模式
npm run test:debug -- --headed
```
**说明**：使用 `playwright.config.debug.ts`，自动启动 Next.js，其他服务（Azure Functions、Mock Server、Azurite）通过 TestServicesManager 在测试中按需启动。
⚠️ **注意**：调试模式也使用 Next.js **生产模式**，确保与自动模式的测试环境一致。

## 📁 项目结构

```
tests/integration/
├── specs/                          # 测试规格文件
│   ├── final-export-test.spec.ts      # 🌟 完整导出流程测试
│   ├── management-item-export.spec.ts # 管理项目导出测试
│   ├── server-list.spec.ts            # 服务器列表测试
│   ├── azure-automation-mock.spec.ts  # Mock Server 测试
│   └── azurite-test.spec.ts           # Azurite 连接测试
├── support/                         # 测试支持文件
│   ├── auth.helper.ts                  # 认证辅助
│   ├── server-data.helper.ts           # 服务器数据管理
│   ├── test-services-manager.ts        # 🌟 智能服务管理器
│   ├── mock-server-helper.ts           # Mock Server 管理
│   ├── azure-automation-mock-server.ts # Azure Automation 模拟
│   ├── network-interceptor.helper.ts   # 网络拦截
│   ├── database.helper.ts              # 数据库操作
│   ├── performance.helper.ts           # 性能监控
│   └── ...                            # 其他辅助工具
├── scripts/                         # 辅助脚本
│   └── start-mock-server.ts           # Mock Server 启动脚本
├── playwright.config.ts             # 自动启动配置（生产测试）
├── playwright.config.debug.ts       # 调试模式配置（开发推荐）
├── README.md                        # 本文档
├── FINAL-SOLUTION.md                # 🌟 最终解决方案详细说明
├── PLAYWRIGHT-CONFIGS.md            # 配置文件说明
├── README-mock-server.md            # Mock Server 详细文档
└── DEFECT-REPORT.md                 # 已知缺陷报告
```

## ⚙️ 配置说明

### 两种运行模式对比

| 运行模式 | 配置文件 | npm 命令 | webServer | 服务启动 | Next.js 模式 | 适用场景 |
|---------|----------|----------|-----------|----------|-------------|----------|
| **自动模式** | `playwright.config.ts` | `npm test` | ✅ 启用 | 自动启动 | 🏭 **生产模式** | CI/CD、一键测试 |
| **调试模式** | `playwright.config.debug.ts` | `npm run test:debug` | 🔧 部分启用 | 自动启动 Next.js | 🏭 **生产模式** | 开发调试、稳定性优先 |

### 配置文件详情

1. **`playwright.config.ts`** - 自动模式配置
   - ✅ **包含 webServer 自动启动功能**
   - ✅ 自动构建并启动 Next.js **生产模式** (端口3000)
   - ✅ 自动启动标准 Azure Functions (端口7072)
   - ✅ 自动启动长时运行 Azure Functions (端口7071)
   - ✅ 智能重试和并发策略
   - ✅ 全面的监控和报告
   - ⚠️ **注意**: 首次运行需要构建时间，但测试环境更接近生产环境

2. **`playwright.config.debug.ts`** - 调试模式配置
   - 🔧 **部分使用 webServer** - 只自动启动 Next.js
   - ✅ 其他服务通过 TestServicesManager 在测试中按需启动
   - ✅ 使用 Next.js **生产模式**
   - ✅ 更稳定，避免 Windows 权限问题
   - ✅ 适合开发调试和逐步验证

### 环境变量

测试环境变量通过以下方式加载：
- `apps/jcs-endpoint-nextjs/.env.test.local` - Next.js 测试环境变量
- `apps/jcs-backend-services-standard/local.settings.test.json` - Azure Functions 测试配置
- `apps/jcs-backend-services-long-running/local.settings.test.json` - 长时运行 Functions 测试配置

## 🔧 核心组件

### 智能服务管理器 (`TestServicesManager`)

**主要特性**：
- ✅ **完全自动化** - 自动启动和停止所有必要服务
- ✅ **端口冲突自动解决** - 启动前自动检测并清理占用端口的进程
- ✅ **智能健康检查** - 支持HTTP检查 + 日志检查 + 进程状态检查
- ✅ **Azurite 集成** - 自动启动和管理 Azurite (Azure Storage 模拟器)
- ✅ **资源清理** - 测试结束后自动清理进程和数据文件

**启动顺序**：
1. 启动 Azurite (Azure Storage 模拟器，端口10000)
2. 启动 Mock Server（端口3001）
3. 启动标准 Azure Functions（端口7072）
4. 启动长时间运行 Azure Functions（端口7071）

## 🧪 测试类型

### 1. 功能测试
- **服务器列表测试** (`server-list.spec.ts`)
  - 服务器列表显示和筛选
  - 用户权限验证
  - 数据创建和清理

- **管理项目导出测试** (`management-item-export.spec.ts`)
  - 导出任务创建和执行
  - 状态跟踪和验证

### 2. Mock Server 测试
- **Azure Automation Mock 测试** (`azure-automation-mock.spec.ts`)
  - Mock Server 功能验证
  - API 模拟测试

### 3. 基础设施测试
- **Azurite 连接测试** (`azurite-test.spec.ts`)
  - 存储服务连接验证
  - 基础设施健康检查

## 🛠️ 开发指南

### 添加新测试

1. 在 `specs/` 目录下创建新的 `.spec.ts` 文件
2. 使用现有的 helper 模块进行数据管理和认证
3. 遵循现有的测试模式和命名约定

### 测试数据管理

- 使用 `server-data.helper.ts` 管理服务器测试数据
- 使用 `auth.helper.ts` 进行用户认证
- 使用 `test-services-manager.ts` 管理服务启动

### 运行特定测试文件

```bash
# 自动模式：运行单个 spec 文件（自动启动服务）
npm test specs/server-list.spec.ts
npm test specs/management-item-export.spec.ts
npm test specs/final-export-test.spec.ts
npm test specs/azure-automation-mock.spec.ts

# 调试模式：运行单个 spec 文件
npm run test:debug specs/server-list.spec.ts
npm run test:debug specs/management-item-export.spec.ts
npm run test:debug specs/final-export-test.spec.ts
npm run test:debug specs/azure-automation-mock.spec.ts

# 运行多个特定文件
npm test specs/server-list.spec.ts specs/final-export-test.spec.ts
npm run test:debug specs/server-list.spec.ts specs/final-export-test.spec.ts
```

### 运行单个测试用例

```bash
# 自动模式：使用 --grep 参数按测试名称匹配（自动启动服务）
npm test -- --grep "应该正确显示服务器列表"
npm test -- --grep "服务器列表"  # 模糊匹配
npm test specs/server-list.spec.ts -- --grep "应该支持按服务器名称过滤"

# 调试模式：使用 --grep 参数
npm run test:debug -- --grep "应该正确显示服务器列表"
npm run test:debug -- --grep "服务器列表"  # 模糊匹配
npm run test:debug specs/server-list.spec.ts -- --grep "应该支持按服务器名称过滤"

# 使用正则表达式匹配多个测试用例
npm run test:debug -- --grep "应该支持.*过滤"
npm run test:debug -- --grep "(排序|过滤)"

# 在代码中使用 test.only() 运行特定测试（临时调试用）
# 在测试文件中将 test('...') 改为 test.only('...')，然后运行：
npm run test:debug specs/server-list.spec.ts
```

### 调试技巧

```bash
# 自动模式：调试特定测试文件
npm run test:debug specs/server-list.spec.ts

# 自动模式：调试特定测试用例（按名称匹配）
npm run test:debug -- --grep "服务器列表"

# 调试模式：调试特定测试文件
npm run test:debug -- --debug specs/server-list.spec.ts

# 调试模式：调试特定测试用例
npm run test:debug -- --debug --grep "服务器列表"

# 有头模式运行特定文件
npm run test:headed specs/server-list.spec.ts
npm run test:debug -- --headed specs/server-list.spec.ts

# 有头模式运行单个测试用例
npm run test:debug -- --headed --grep "应该正确显示服务器列表"

# 调试单个测试用例的完整示例
npm run test:debug specs/server-list.spec.ts -- --headed --debug --grep "应该能够刷新服务器列表"

# 查看测试报告
npm run show-report

# 查看测试轨迹
npm run show-trace
```

## 📊 测试报告

测试完成后，报告文件位于：
- HTML 报告：`../test-results/html-report/`
- JSON 结果：`../test-results/test-results.json`
- JUnit 报告：`../test-results/junit-report.xml`

## 🔧 故障排除

### 常见问题

1. **终端环境问题**
   - 确保使用 Bash 终端（Git Bash、WSL 等）
   - PowerShell 和 CMD 无法运行项目脚本
   - 如果在 Windows 上，推荐使用 Git Bash

2. **测试超时**
   - 检查服务是否正确启动
   - 增加测试超时时间

3. **Azurite 启动失败**
   - 检查端口 10000 是否被其他程序占用
   - 确保有足够的磁盘空间用于 Azurite 数据存储
   - 项目会自动启动和管理 Azurite（只使用 Blob 服务）

4. **数据库连接问题**
   - 确认数据库服务正在运行
   - 检查环境变量配置

5. **认证失败**
   - 检查 session 配置
   - 验证用户权限设置

### 清理命令

```bash
# 清理测试结果
npm run clean-results

# 清理数据库（在 helper 中实现）
# 测试会自动清理创建的测试数据
```

## 📝 最佳实践

1. **数据隔离** - 每个测试使用独立的测试数据
2. **并发安全** - 使用动态生成的许可证ID避免冲突
3. **自动清理** - 测试后自动清理创建的数据
4. **错误处理** - 完善的错误处理和重试机制
5. **性能监控** - 集成性能监控和报告

## 🚫 测试环境限制与缺陷处理规则

### 测试环境限制
1. **禁止修改业务代码**：集成测试环境使用生产模式构建，**严禁**为了让测试通过而修改 `apps/` 目录下的业务代码
2. **测试代码修改原则**：只能修改测试代码本身（`tests/integration/` 目录下的文件）来适应业务逻辑
3. **环境一致性**：测试环境应该尽可能接近生产环境，不应为测试便利性而改变业务逻辑

### 缺陷识别与处理流程
当测试失败时，按以下步骤判断：

1. **首先排查测试问题**：
   - 检查测试数据创建是否正确
   - 检查页面等待逻辑是否充分
   - 检查元素选择器是否准确
   - 检查网络请求是否正常

2. **确认为业务缺陷时**：
   - 在 `tests/integration/DEFECT-REPORT.md` 中记录缺陷
   - 包含详细的重现步骤和期望行为
   - 标记相关的测试用例为已知缺陷
   - 通知开发团队进行修复

3. **缺陷记录格式**：
   ```markdown
   ## DEF-YYYY-MM-DD-001: 缺陷标题
   - **发现日期**: YYYY-MM-DD
   - **测试用例**: 具体的测试用例名称
   - **测试文件**: specs/xxx.spec.ts:行号
   - **缺陷类型**: 业务逻辑缺陷/数据缓存问题/UI交互问题等
   - **SSD参考**: 相关设计文档路径（如适用）

   ### 重现步骤
   详细的步骤列表

   ### 实际结果
   当前的错误行为描述

   ### 期望结果
   根据需求或SSD文档的正确预期行为

   ### 技术分析
   问题的技术根因分析

   ### 相关代码文件
   涉及的源代码文件列表

   ### 修复建议
   具体的修复方案建议

   ### 测试证据
   测试日志、截图等证据

   ### 影响范围
   功能影响、用户体验影响、业务影响评估

   ### 状态
   - **当前状态**: 待修复/修复中/已修复
   - **优先级**: 高/中/低
   - **分配给**: 开发团队
   ```

4. **缺陷记录规则**：
   - **强制记录**：所有通过测试发现的业务逻辑缺陷都必须记录到 `DEFECT-REPORT.md`
   - **SSD对照**：如果有相关的SSD设计文档，必须引用并对照分析
   - **测试适配**：记录缺陷后，测试代码应适配当前实现状态，避免因已知缺陷导致测试失败
   - **状态跟踪**：定期更新缺陷状态，修复后及时更新记录
   - **统计维护**：保持缺陷统计信息的准确性

## ⚠️ 重要提醒

1. **🖥️ 必须使用 Bash 终端** - 项目脚本和配置依赖 Bash 环境，PowerShell 和 CMD 不支持
2. **应用需要预先构建** - Next.js 和 Azure Functions 需要预先构建
3. **自动服务管理** - 测试会自动启动和停止所有必要服务（包括 Azurite）
4. **端口管理** - 测试会自动处理端口冲突，清理占用的进程
5. **数据隔离** - 每个测试使用独立的许可证ID，确保测试间不互相影响

## 📚 相关文档
- [最终解决方案详细说明](./FINAL-SOLUTION.md) - 完整的技术实现和解决方案
- [Mock Server 文档](./README-mock-server.md) - Azure Automation Mock Server 使用指南
- [配置文件说明](./PLAYWRIGHT-CONFIGS.md) - 两种测试配置的详细对比
- [已知缺陷报告](./DEFECT-REPORT.md) - 当前已知问题和解决方案

---

**最后更新**: 2025-07-31
**维护者**: 开发团队
**版本**: 1.1