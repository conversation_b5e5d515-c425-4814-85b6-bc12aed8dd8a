{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "node", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "MSSQL_PRISMA_URL": "sqlserver://**************:65220;database=test;user=SA;password=Uknowit1^_^;encrypt=DANGER_PLAINTEXT;", "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME": "Endpoint=sb://**************:32445;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=SAS_KEY_VALUE;UseDevelopmentEmulator=true;", "SERVICE_BUS_TASK_INPUT_QUEUE_NAME": "task-input-queue-test", "SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME": "runbook-status-queue-test", "SERVICE_BUS_TASK_CONTROL_QUEUE_NAME": "task-control-queue-test", "AZURE_STORAGE_BLOB_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AZURE_STORAGE_FILES_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF": "assetsfield-def-test", "AZURE_STORAGE_CONTAINER_OPLOGS": "oplogs-test", "AZURE_AUTOMATION_ACCOUNT_NAME": "aa-uat-ep-eastus", "SUBSCRIPTION_ID": "ef7e9c57-0352-4755-a3c7-55a9356cab2a", "RESOURCE_GROUP_NAME": "rg-uat-ep-eastus", "RUNBOOK_MGMT_ITEM_IMPORT": "Import-Management-Item-Test", "RUNBOOK_MGMT_ITEM_EXPORT": "Export-Management-Item-Test", "RUNBOOK_OPLOG_EXPORT": "Export-Operation-Log-Test", "RUNBOOK_MONITOR_INTERVAL_SECONDS": 30, "RUNBOOK_TIMEOUT_SECONDS": 18000, "AZURE_MANAGEMENT_BASE_URL": "http://localhost:3001"}}