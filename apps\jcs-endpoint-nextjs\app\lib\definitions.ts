/**
 * @file definitions.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

// This file contains type definitions for your data.
// It describes the shape of the data, and what data type each property should accept.
// For simplicity of teaching, we're manually defining these types.

import { License, Lov } from "@prisma/client";

export const navLinks = [
  {
    name: "管理",
    key: "management",
    subs: [
      { name: "サーバ一覧", href: "/dashboard/servers" },
      { name: "タスク一覧", href: "/dashboard/tasks" },
    ],
  },
  {
    name: "ファイル",
    key: "file",
    subs: [
      { name: "操作ログ一覧", href: "/dashboard/oplogs" },
      { name: "製品媒体一覧", href: "/dashboard/medias" },
      { name: "マニュアル一覧", href: "/dashboard/manuals" },
      { name: "提供ファイル一覧", href: "/dashboard/provided-files" },
      { name: "サポート情報一覧", href: "/dashboard/support-files" },
    ],
  },
];

export const ENV = {
  APP_CACHE_TTL_SECONDS: process.env.APP_CACHE_TTL_SECONDS
    ? Number(process.env.APP_CACHE_TTL_SECONDS)
    : 7200,
  AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_STORAGE_CONNECTION_STRING,
  AZURE_STORAGE_ACCOUNT_NAME: process.env.AZURE_STORAGE_ACCOUNT_NAME,
  AZURE_SERVICEBUS_NAMESPACE_HOSTNAME:
    process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME,
  AZURE_SERVICEBUS_CONNECTION_STRING: process.env.AZURE_SERVICEBUS_CONNECTION_STRING,
  MSSQL_PRISMA_URL: process.env.MSSQL_PRISMA_URL,
  AZURE_AUTOMATION_ACCOUNT_NAME: process.env.AZURE_AUTOMATION_ACCOUNT_NAME,
  JWT_MAX_AGE_SECONDS: process.env.JWT_MAX_AGE_SECONDS
    ? Number(process.env.JWT_MAX_AGE_SECONDS)
    : 1800,
  LOG_LEVEL: process.env.LOG_LEVEL || "info",
  KEYCLOAK_INTERNAL_DOMAIN_NAME: process.env.KEYCLOAK_INTERNAL_DOMAIN_NAME,
  KEYCLOAK_PUBLIC_DOMAIN_NAME: process.env.KEYCLOAK_PUBLIC_DOMAIN_NAME,
  KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
  KEYCLOAK_CLIENT: process.env.KEYCLOAK_CLIENT,
  KEYCLOAK_REDIRECT_URL: process.env.KEYCLOAK_REDIRECT_URL,
  KEYCLOAK_CLIENT_SECRET: process.env.KEYCLOAK_CLIENT_SECRET,
  AZURE_STORAGE_CONTAINER_OPLOGS:
    process.env.AZURE_STORAGE_CONTAINER_OPLOGS || "oplogs",
  AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS:
    process.env.AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS || "product-medias",
  AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS:
    process.env.AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS || "product-manuals",
  AZURE_STORAGE_CONTAINER_PROVIDED_FILES:
    process.env.AZURE_STORAGE_CONTAINER_PROVIDED_FILES || "provided-files",
  AZURE_STORAGE_CONTAINER_SUPPORT_FILES:
    process.env.AZURE_STORAGE_CONTAINER_SUPPORT_FILES || "support-files",
  AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF:
    process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF || "assetsfield-def",
  SERVICE_BUS_TASK_INPUT_QUEUE_NAME:
    process.env.SERVICE_BUS_TASK_INPUT_QUEUE_NAME || "task-input-queue",
  SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME:
    process.env.SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME || "runbook-status-queue",
  SERVICE_BUS_TASK_CONTROL_QUEUE_NAME:
    process.env.SERVICE_BUS_TASK_CONTROL_QUEUE_NAME || "task-control-queue",
  SESSION_SECRET:
    process.env.SESSION_SECRET || "complex_password_at_least_32_characters_long",
};

export const PORTAL_ERROR_MESSAGES = {
  EMEC0002: "確認用パスワードが正しくありません。",
  EMEC0003: "現在のパスワードが正しくありません。",
  EMEC0005: "現在ポータルは利用できません。",
  EMEC0006:
    "データベースに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
  EMEC0007:
    "サーバに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
  EMEC0008: "パスワードを変更しました。",
  EMEC0009:
    "新しいパスワードは8文字以上、128文字以下のパスワードを入力してください。",
  EMEC0010:
    "新しいパスワードには2種類以上の文字の組み合わせを入力してください。",
  EMEC0011: "新しいパスワードにはユーザーIDと異なる文字列を入力してください。",
  EMEC0012:
    "新しいパスワードには現在のパスワードと異なる文字列を入力してください。",
  EMEC0013: "パスワード変更に失敗しました。",
  EMEC0014: "ワンタイムコードが正しくありません。",
  EMEC0015: "現在のパスワードまたはワンタイムコードが正しくありません。",
  EMEC0016: "{0}を指定してください。",
  EMEC0017: "無効なファイル形式です。CSVファイルを指定してください。",
  EMEC0018:
    "ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。",
  EMEC0019:
    "サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)",
  EMEC0020:
    "{0} 日を超える期間が指定されました。{1} 日以内の期間を指定して再度実行してください。",
  EMEC0021:
    "サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)",
  EMEC0022:
    "{0}に対するタスクを実行中のため実行できません。<br/>実行中のタスクが完了してから再度実行してください。",
  EMEC0023: "タスクの中止はできません。タスクは他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています。",
  EMEC0024: "終了日は開始日以降の日付を指定してください。",
  EMEC0025:
    "タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。\n対象サーバ：{0}\nタスク種別：{1}",
  EMEC0026:
    "タスクの中止を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\nタスク名：{0}",
  EMEC0027:
    "サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027)",
  EMEC0028: "インポートできるファイルサイズの上限を超えています。10MB以下のファイルを指定してください。"
};

/**
 * ライセンスタイプ（LICENSE_TYPE）LOVのコード定数。
 * ライセンスの種別を一意に識別するために使用します。
 */
export const LICENSE_TYPE = "LICENSE_TYPE";

/**
 * ライセンスタイプ（LICENSE_TYPE）LOVの列挙型。
 * 各値はLOV定義のcodeと完全一致します。
 */
export enum LicenseType {
  PROD = "LICENSE_TYPE.PROD", // 本番ライセンス
  TRIAL = "LICENSE_TYPE.TRIAL", // トライアルライセンス
}

export enum NotificationType {
  SYSTEM = "SYSTEM",
  LICENSE = "LICENSE",
  PLAN = "PLAN",
  USER = "USER",
}

/**
 * サーバータイプ（SERVER_TYPE）LOVのコード定数。
 * サーバーの種別を一意に識別するために使用します。
 * LOV定義は管理画面のサーバー登録・編集等で利用されます。
 */
export const SERVER_TYPE = "SERVER_TYPE";
export const TASK_TYPE_LOV = "TASK_TYPE";
export const TASK_STATUS_LOV = "TASK_STATUS";

/**
 * サーバータイプ（SERVER_TYPE）LOVの列挙型。
 * 各値はLOV定義のcodeと完全一致します。
 */
export enum ServerType {
  GENERAL_MANAGER = "SERVER_TYPE.GENERAL_MANAGER", // 一般管理サーバー
  RELAY_MANAGER = "SERVER_TYPE.RELAY_MANAGER", // 中継管理サーバー
  HIBUN_CONSOLE = "SERVER_TYPE.HIBUN_CONSOLE", // HIBUNコンソールサーバー
}

/**
 * OSタイプ（OS_TYPE）LOVの列挙型。
 * サーバーのOS種別を一意に識別します。
 * 各値はLOV定義のcodeと一致します。
 */
export const OS_TYPE = "OS_TYPE";

/**
 * サポート情報重要度（SUPPORT_IMPORTANCE）LOVの列挙型。
 * サポート情報の重要度区分を表します。
 * 各値はLOV定義のcodeと一致します。
 */
export const SUPPORT_IMPORTANCE = "SUPPORT_IMPORTANCE";

export interface LicenseAPI extends License {
  typeLov: Lov;
}

export interface ModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onOK?: (message: string) => void;
  onError?: (error: string) => void;
}

export const PRODUCT_MEDIA_LIST_DEFAULT_SECOND_ORDER = "name";
export const PROVIDED_FILE_LIST_DEFAULT_SECOND_ORDER = "name";
export const SUPPORT_FILE_LIST_DEFAULT_SECOND_ORDER = "productName";

/**
 * アカウントロックアウト関連のLOVコード定数。
 * 各値はLOV定義のcodeと一致し、ロックアウト設定値取得に利用します。
 */
export const LOV_CODE_LOCKOUT_MAX_FAILED_TIMES = "LOCKOUT.MAX_FAILED_TIMES";
export const LOV_CODE_LOCKOUT_TIME_SECONDS = "LOCKOUT.TIME_SECONDS";

export const atLeastTwoKindsRegex = new RegExp(
  /^(?:(?=(?:.*[A-Za-z]))(?=(?:.*\d))(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~])|.*[A-Za-z\d])|(?=(?:.*\d))(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))(?=(?:.*[A-Za-z])|.*\d)|(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))(?=(?:.*[A-Za-z]))(?=(?:.*\d)|.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))[A-Za-z\d$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]{8,}$/,
);

/**
 * Azureストレージ関連のLOVコード定数。
 * 各値はLOV定義のcodeと完全一致し、Blobコンテナ名やSAS TTL等の設定取得に利用します。
 */
export const LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS =
  "AZURE_STORAGE.CONTAINER_OPLOGS";
export const LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS =
  "AZURE_STORAGE.CONTAINER_PRODUCT_MEDIAS";
export const LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS =
  "AZURE_STORAGE.CONTAINER_PRODUCT_MANUALS";
export const LOV_CODE_AZURE_STORAGE_CONTAINER_PROVIDED_FILES =
  "AZURE_STORAGE.CONTAINER_PROVIDED_FILES";
export const LOV_CODE_AZURE_STORAGE_CONTAINER_SUPPORT_FILES =
  "AZURE_STORAGE.CONTAINER_SUPPORT_FILES";
export const LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF =
  "AZURE_STORAGE.CONTAINER_ASSETSFIELD_DEF";
export const LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS =
  "AZURE_STORAGE.SAS_TTL_SECONDS";

export const LoginFormUserIdPattern = new RegExp(/^[a-z0-9.]+$/);
export const LoginFormPasswordPattern = new RegExp(
  /^[A-Za-z\d$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]+$/,
);
export const IsNumber = new RegExp(/^\d+$/);
export const PORTAL_CACHE_KEY_LOV_LIST = "lov-list";
export const PORTAL_CACHE_KEY_LOV = "lov";
export const PORTAL_CACHE_KEY_SERVERS = "servers";
export const PORTAL_CACHE_KEY_TASKS = "tasks";
export const PORTAL_CACHE_KEY_OPLOGS = "oplogs";
export const PORTAL_CACHE_KEY_MEDIAS = "medias";
export const PORTAL_CACHE_KEY_MANUALS = "manuals";
export const PORTAL_CACHE_KEY_PROVIDED_FILES = "provided-files";
export const PORTAL_CACHE_KEY_SUPPORT_FILES = "support-files";
export const LOV_CODE_OPLOG_EXPORT_MAX_DAYS =
  "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN";
export const LOV_CODE_OPLOG_EXPORT_ALLOWED_PLANS =
  "OPERATION_LOG_CONFIG.ALLOWED_PLANS";
export const LOV_CODE_OPLOG_EXPORT_ALLOW_STANDARD_PLAN =
  "OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_STANDARD_PLAN";
export const LOV_CODE_OPLOG_EXPORT_ALLOW_LIGHT_B_PLAN =
  "OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_LIGHT_B_PLAN";

/**
 * タスク関連のServer Actionの標準的な戻り値の型定義。
 * タスクの作成、中止などの操作結果を表現する。
 * @property {boolean} success - 操作が正常に受け付けられたかどうかを示す。
 * @property {string} message - ユーザー向け日本語メッセージ（パラメータ埋め込み済み）。
 */
export interface TaskActionResult {
  /**
   * タスク操作リクエストの成否
   */
  success: boolean;
  /**
   * ユーザー向け日本語メッセージ（パラメータ埋め込み済み）
   */
  message: string;
}

/**
 * タスク種別（TASK_TYPE）LOVのコード定数。
 * 各値はLOV定義のcodeと完全一致し、タスク種別の判定や登録に利用する。
 */
export const TASK_TYPE = {
  OPLOG_EXPORT: "TASK_TYPE.OPLOG_EXPORT", // 操作ログのエクスポート
  MGMT_ITEM_IMPORT: "TASK_TYPE.MGMT_ITEM_IMPORT", // 管理項目定義のインポート
  MGMT_ITEM_EXPORT: "TASK_TYPE.MGMT_ITEM_EXPORT", // 管理項目定義のエクスポート
};

/**
 * タスク状態（TASK_STATUS）LOVのコード定数。
 * 各値はLOV定義のcodeと完全一致し、タスク進捗管理や画面表示に利用する。
 */
export const TASK_STATUS = {
  QUEUED: "TASK_STATUS.QUEUED", // 実行待ち
  RUNBOOK_SUBMITTED: "TASK_STATUS.RUNBOOK_SUBMITTED", // Runbookジョブ作成完了
  RUNBOOK_PROCESSING: "TASK_STATUS.RUNBOOK_PROCESSING", // Runbookジョブ処理中
  COMPLETED_SUCCESS: "TASK_STATUS.COMPLETED_SUCCESS", // 正常終了
  COMPLETED_ERROR: "TASK_STATUS.COMPLETED_ERROR", // エラー
  CANCELLED: "TASK_STATUS.CANCELLED", // 中止
  PENDING_CANCELLATION: "TASK_STATUS.PENDING_CANCELLATION", // 中止待ち
};

/**
 * タスク操作の種別名
 * 主にエラーメッセージのフォーマットに使用されます。
 */
export const TASK_ACTION_NAME = {
  START: "開始",
  CANCEL: "中止",
};

/**
 * コンテナの並行性ステータス
 */
export const CONTAINER_STATUS = {
  IDLE: "IDLE",
  BUSY: "BUSY",
} as const;

// 操作系统类型 LOV
/**
 * OSタイプ（OS_TYPE）LOVの列挙型。
 * サーバーのOS種別を一意に識別します。
 * 各値はLOV定義のcodeと一致します。
 */
export enum OSType {
  WIN = "OS_TYPE.WIN", // Windows
  LINUX = "OS_TYPE.LINUX", // Linux
  AIX = "OS_TYPE.AIX", // AIX
  SOLARIS = "OS_TYPE.SOLARIS", // Solaris
  HPUX = "OS_TYPE.HPUX", // HP-UX
  MACOS = "OS_TYPE.MACOS", // macOS
}

/**
 * サポート情報重要度（SUPPORT_IMPORTANCE）LOVの列挙型。
 * サポート情報の重要度区分を表します。
 * 各値はLOV定義のcodeと一致します。
 */
export enum SupportImportance {
  NONE = "SUPPORT_IMPORTANCE.NONE", // 重要度なし
  AAA = "SUPPORT_IMPORTANCE.AAA", // AAA
  AA = "SUPPORT_IMPORTANCE.AA", // AA
  A = "SUPPORT_IMPORTANCE.A", // A
  B = "SUPPORT_IMPORTANCE.B", // B
  C = "SUPPORT_IMPORTANCE.C", // C
}

/**
 * フォームパラメータ名称定数
 * エラーメッセージで使用するパラメータ名称の定数定義
 */
export const FORM_FIELD_NAMES = {
  START_DATE: "開始日",
  END_DATE: "終了日",
  MGMT_ITEM_CSV_FILE: "ファイル",
  MAX_DAYS: "最大日数",
} as const;

/**
 * Azure Blob Storage関連の定数
 */
export const AZURE_BLOB_PATHS = {
  IMPORTS_PREFIX: "imports",
  EXPORTS_PREFIX: "exports",
} as const;

/**
 * ファイル名関連の定数
 */
export const FILE_NAMES = {
  ASSETSFIELD_DEF_CSV: "assetsfield_def.csv",
  ERROR_DETAIL_TXT: "errordetail.txt",
} as const;

/**
 * 日付フォーマット関連の定数
 */
export const DATE_FORMATS = {
  DISPLAY_WITH_SPACES: "YYYY / MM / DD",
  DISPLAY_WITH_SLASHES: "YYYY/MM/DD",
  ISO_DATE: "YYYY-MM-DD",
} as const;

/**
 * 管理項目定義インポート用のBlob Storageパス・URLテンプレート
 */
export const BLOB_STORAGE_BASE_URL_TEMPLATE =
  "https://{account}.blob.core.windows.net";
export const MGMT_DEF_IMPORT_BLOB_PATH_TEMPLATE =
  `{licenseId}/${AZURE_BLOB_PATHS.IMPORTS_PREFIX}/{taskId}/${FILE_NAMES.ASSETSFIELD_DEF_CSV}`;

/**
 * 管理項目定義エクスポート用のBlob Storageパステンプレート
 */
export const MGMT_DEF_EXPORT_BLOB_PATH_TEMPLATE =
  `{licenseId}/${AZURE_BLOB_PATHS.EXPORTS_PREFIX}/{taskId}/${FILE_NAMES.ASSETSFIELD_DEF_CSV}`;

/**
 * ファイルアップロード検証用の定数
 * セキュリティ強化のため、DoS攻撃防止と適切な入力検証を実施する
 */
export const FILE_VALIDATION = {
  // CSVファイル検証設定
  CSV: {
    // 許可される拡張子（小文字）
    ALLOWED_EXTENSIONS: [".csv"] as const,
    // 許可されるMIMEタイプ
    ALLOWED_MIME_TYPES: ["text/csv", "application/csv"] as const,
    // 最大ファイルサイズ（バイト）- DoS攻撃防止のため10MBに制限
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  },
} as const;

/**
 * タスク一覧表示用の型定義
 *
 * タスク一覧ページで表示されるタスク情報の構造を定義する。
 * Prismaのタスクモデルに表示用の変換処理を適用したもの。
 */
export interface TaskForList {
  /** タスクID（主キー） */
  id: string;
  /** タスク名 */
  taskName: string | null;
  /** タスクステータス */
  status: string;
  /** タスクステータス（LOVから変換された日本語ラベル） */
  statusLabel: string;
  /** 開始日時（Date型、表示時にフォーマット） */
  startedAt: Date | null;
  /** 終了日時（Date型、表示時にフォーマット） */
  endedAt: Date | null;
  /** 対象サーバー名 */
  targetServerName: string | null;
  /** タスク種別 */
  taskType: string;
  /** タスク種別（LOVから変換された日本語ラベル） */
  taskTypeLabel: string;
  /** 実行ユーザーID */
  submittedByUserId: string;
  /** 結果メッセージ */
  resultMessage: string | null;
  /** ライセンスID */
  licenseId: string;
  /** 提出日時 */
  submittedAt: Date;
  /** 更新日時 */
  updatedAt: Date;
  /** 対象サーバーID */
  targetServerId: string;
  /** 対象コンテナ名 */
  targetContainerName: string | null;
  /** 対象HRWグループ名 */
  targetHRWGroupName: string | null;
  /** 対象VM名 */
  targetVmName: string | null;
  /** パラメータJSON */
  parametersJson: string | null;
  /** エラーメッセージ */
  errorMessage: string | null;
  /** エラーコード */
  errorCode: string | null;
}


