/**
 * @fileoverview Azure Automation Mock Server for Integration Testing
 * @description
 * Azure Automation REST APIをモックするHTTPサーバー。
 * 集成测试时模拟Azure Automation服务的各种API端点，包括作业创建、状态查询、停止等操作。
 * 支持不同的作业状态模拟和错误场景测试。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import express, { Request, Response } from 'express';
import { Server } from 'http';
import { ShareServiceClient } from '@azure/storage-file-share';

/**
 * Azure Automation作业状态枚举
 */
export enum MockJobStatus {
  New = 'New',
  Activating = 'Activating', 
  Running = 'Running',
  Completed = 'Completed',
  Failed = 'Failed',
  Stopped = 'Stopped',
  Blocked = 'Blocked',
  Suspended = 'Suspended',
  Disconnected = 'Disconnected',
  Suspending = 'Suspending',
  Stopping = 'Stopping'
}

/**
 * Mock作业数据接口
 */
interface MockJob {
  id: string;
  name: string;
  status: MockJobStatus;
  startTime?: string;
  endTime?: string;
  runbookName: string;
  parameters: Record<string, any>;
  createdAt: Date;
  lastStatusChange: Date;
}

/**
 * 失败场景配置接口
 */
interface FailureScenario {
  endpoint: string;
  method: string;
  statusCode: number;
  errorResponse: any;
  enabled: boolean;
}

/**
 * Azure Automation Mock Server类
 */
export class AzureAutomationMockServer {
  private app: express.Application;
  private server: Server | null = null;
  private jobs: Map<string, MockJob> = new Map();
  private failureScenarios: Map<string, FailureScenario> = new Map();
  private port: number;
  private shareServiceClient: ShareServiceClient | null = null;

  constructor(port: number = 3001) {
    this.port = port;
    this.app = express();
    this.initializeAzureFiles();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * 中间件设置
   */
  private setupMiddleware(): void {
    this.app.use(express.json());
    
    // CORS设置
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // 请求日志
    this.app.use((req, res, next) => {
      console.log(`[Mock Server] ${req.method} ${req.path}`);
      next();
    });

    // 失败场景检查中间件
    this.app.use((req, res, next) => {
      const key = `${req.method}:${req.path}`;
      const scenario = this.failureScenarios.get(key);

      if (scenario && scenario.enabled) {
        console.log(`[Mock Server] Triggering failure scenario for ${key}`);
        return res.status(scenario.statusCode).json(scenario.errorResponse);
      }

      next();
    });
  }

  /**
   * 路由设置
   */
  private setupRoutes(): void {
    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // 创建Runbook作业
    this.app.put('/subscriptions/:subscriptionId/resourceGroups/:resourceGroupName/providers/Microsoft.Automation/automationAccounts/:automationAccount/jobs/:jobName', 
      this.createJob.bind(this));

    // 获取作业状态
    this.app.get('/subscriptions/:subscriptionId/resourceGroups/:resourceGroupName/providers/Microsoft.Automation/automationAccounts/:automationAccount/jobs/:jobName',
      this.getJobStatus.bind(this));

    // 停止作业
    this.app.post('/subscriptions/:subscriptionId/resourceGroups/:resourceGroupName/providers/Microsoft.Automation/automationAccounts/:automationAccount/jobs/:jobName/stop',
      this.stopJob.bind(this));

    // 清理所有作业（测试用）
    this.app.delete('/mock/jobs', this.clearAllJobs.bind(this));

    // 设置作业状态（测试用）
    this.app.put('/mock/jobs/:jobName/status', this.setJobStatus.bind(this));

    // 获取所有作业（测试用）
    this.app.get('/mock/jobs', this.getAllJobs.bind(this));

    // 失败场景管理端点
    this.app.put('/mock/failure-scenarios', this.setFailureScenario.bind(this));
    this.app.delete('/mock/failure-scenarios', this.clearFailureScenarios.bind(this));
    this.app.get('/mock/failure-scenarios', this.getFailureScenarios.bind(this));
  }

  /**
   * 创建Runbook作业
   */
  private createJob(req: Request, res: Response): any {
    const { jobName } = req.params;
    const { properties } = req.body;

    if (!properties || !properties.runbook || !properties.runbook.name) {
      return res.status(400).json({
        error: {
          code: 'BadRequest',
          message: 'Missing required runbook information'
        }
      });
    }

    const job: MockJob = {
      id: `job-${Date.now()}`,
      name: jobName,
      status: MockJobStatus.New,
      startTime: new Date().toISOString(),
      runbookName: properties.runbook.name,
      parameters: properties.parameters || {},
      createdAt: new Date(),
      lastStatusChange: new Date()
    };

    this.jobs.set(jobName, job);

    // 模拟作业状态变化：New -> Activating -> Running
    setTimeout(() => this.updateJobStatus(jobName, MockJobStatus.Activating), 100);
    setTimeout(() => this.updateJobStatus(jobName, MockJobStatus.Running), 500);

    res.status(201).json({
      id: job.id,
      name: job.name,
      properties: {
        jobId: job.id,
        runbook: {
          name: job.runbookName
        },
        status: job.status,
        startTime: job.startTime,
        parameters: job.parameters
      }
    });
  }

  /**
   * 获取作业状态
   */
  private getJobStatus(req: Request, res: Response): any {
    const { jobName } = req.params;
    const job = this.jobs.get(jobName);

    if (!job) {
      return res.status(404).json({
        error: {
          code: 'NotFound',
          message: `Job '${jobName}' not found`
        }
      });
    }

    res.json({
      id: job.id,
      name: job.name,
      properties: {
        jobId: job.id,
        runbook: {
          name: job.runbookName
        },
        status: job.status,
        startTime: job.startTime,
        endTime: job.endTime,
        parameters: job.parameters,
        lastStatusModifiedTime: job.lastStatusChange.toISOString()
      }
    });
  }

  /**
   * 停止作业
   */
  private stopJob(req: Request, res: Response): any {
    const { jobName } = req.params;
    const job = this.jobs.get(jobName);

    if (!job) {
      return res.status(404).json({
        error: {
          code: 'NotFound',
          message: `Job '${jobName}' not found`
        }
      });
    }

    if (job.status === MockJobStatus.Completed || job.status === MockJobStatus.Failed || job.status === MockJobStatus.Stopped) {
      return res.status(400).json({
        error: {
          code: 'BadRequest',
          message: `Job '${jobName}' is already in terminal state: ${job.status}`
        }
      });
    }

    this.updateJobStatus(jobName, MockJobStatus.Stopping);
    setTimeout(() => this.updateJobStatus(jobName, MockJobStatus.Stopped), 200);

    res.status(200).json({
      message: `Job '${jobName}' stop request accepted`
    });
  }

  /**
   * 更新作业状态
   */
  private updateJobStatus(jobName: string, status: MockJobStatus): void {
    const job = this.jobs.get(jobName);
    if (job) {
      job.status = status;
      job.lastStatusChange = new Date();

      if (status === MockJobStatus.Completed || status === MockJobStatus.Failed || status === MockJobStatus.Stopped) {
        job.endTime = new Date().toISOString();
      }

      // 当作业状态变为 Completed 时，创建导出文件（模拟 Runbook 执行结果）
      if (status === MockJobStatus.Completed) {
        this.createExportFile(jobName).catch(error => {
          console.error(`[Mock Server] Failed to create export file for job ${jobName}:`, error);
        });
      }
    }
  }

  /**
   * 清理所有作业（测试用）
   */
  private clearAllJobs(req: Request, res: Response): any {
    this.jobs.clear();
    res.json({ message: 'All jobs cleared' });
  }

  /**
   * 设置作业状态（测试用）
   */
  private setJobStatus(req: Request, res: Response): any {
    const { jobName } = req.params;
    const { status } = req.body;

    if (!Object.values(MockJobStatus).includes(status)) {
      return res.status(400).json({
        error: 'Invalid status. Valid statuses: ' + Object.values(MockJobStatus).join(', ')
      });
    }

    const job = this.jobs.get(jobName);
    if (!job) {
      return res.status(404).json({ error: `Job '${jobName}' not found` });
    }

    this.updateJobStatus(jobName, status);
    res.json({ message: `Job '${jobName}' status set to '${status}'` });
  }

  /**
   * 获取所有作业（测试用）
   */
  private getAllJobs(req: Request, res: Response): any {
    const jobsArray = Array.from(this.jobs.values()).map(job => ({
      name: job.name,
      status: job.status,
      runbookName: job.runbookName,
      createdAt: job.createdAt,
      lastStatusChange: job.lastStatusChange
    }));

    res.json({ jobs: jobsArray, count: jobsArray.length });
  }

  /**
   * 设置失败场景（测试用）
   */
  private setFailureScenario(req: Request, res: Response): any {
    const { endpoint, method, statusCode, errorResponse, enabled = true } = req.body;

    if (!endpoint || !method || !statusCode) {
      return res.status(400).json({
        error: 'Missing required fields: endpoint, method, statusCode'
      });
    }

    const key = `${method.toUpperCase()}:${endpoint}`;
    const scenario: FailureScenario = {
      endpoint,
      method: method.toUpperCase(),
      statusCode,
      errorResponse: errorResponse || { error: { code: 'MockError', message: 'Simulated failure' } },
      enabled
    };

    this.failureScenarios.set(key, scenario);
    res.json({ message: `Failure scenario set for ${key}`, scenario });
  }

  /**
   * 清理所有失败场景（测试用）
   */
  private clearFailureScenarios(req: Request, res: Response): any {
    this.failureScenarios.clear();
    res.json({ message: 'All failure scenarios cleared' });
  }

  /**
   * 获取所有失败场景（测试用）
   */
  private getFailureScenarios(req: Request, res: Response): any {
    const scenarios = Array.from(this.failureScenarios.values());
    res.json({ scenarios, count: scenarios.length });
  }

  /**
   * 启动Mock Server
   */
  public async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`[Azure Automation Mock Server] Started on port ${this.port}`);
        resolve();
      });

      this.server.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 停止Mock Server
   */
  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('[Azure Automation Mock Server] Stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 获取服务器URL
   */
  public getBaseUrl(): string {
    return `http://localhost:${this.port}`;
  }

  /**
   * 初始化 Azure Files 客户端
   */
  private initializeAzureFiles(): void {
    try {
      // 直接从环境变量获取连接字符串
      const connectionString = process.env.AZURE_STORAGE_FILES_CONNECTION_STRING;

      if (!connectionString) {
        console.warn('[Mock Server] AZURE_STORAGE_FILES_CONNECTION_STRING not found, skipping Azure Files initialization');
        return;
      }

      // 直接创建 Azure Files 客户端
      this.shareServiceClient = ShareServiceClient.fromConnectionString(connectionString);
      console.log('[Mock Server] Azure Files client initialized successfully');
    } catch (error) {
      console.warn('[Mock Server] Failed to initialize Azure Files client:', error);
    }
  }

  /**
   * 模拟 Runbook 执行完成后创建导出文件
   * 当作业状态变为 Completed 时调用此方法
   */
  public async createExportFile(taskId: string): Promise<void> {
    if (!this.shareServiceClient) {
      console.warn('[Mock Server] Azure Files client not initialized');
      return;
    }

    try {
      const shareName = 'taskworkspaces';
      const shareClient = this.shareServiceClient.getShareClient(shareName);

      // 确保共享存在
      await shareClient.createIfNotExists();

      // 创建任务目录路径
      const taskDirectoryPath = `${taskId}/exports`;
      const directoryClient = shareClient.getDirectoryClient(taskDirectoryPath);

      // 确保目录存在
      await directoryClient.createIfNotExists();

      // 创建导出文件 - 使用业务逻辑期望的固定文件名
      const fileName = 'assetsfield_def.csv';
      const fileClient = directoryClient.getFileClient(fileName);

      // 创建一个简单的 CSV 格式测试文件内容
      const testContent = `id,name,type,description\n1,test-field-${taskId},string,Test field generated at ${new Date().toISOString()}`;
      const buffer = Buffer.from(testContent, 'utf8');

      // 上传文件
      await fileClient.create(buffer.length);
      await fileClient.uploadRange(buffer, 0, buffer.length);

      console.log(`[Mock Server] Created export file: ${shareName}/${taskDirectoryPath}/${fileName}`);
    } catch (error) {
      console.error('[Mock Server] Failed to create export file:', error);
    }
  }
}
