/**
 * 最终版本的测试用例转换脚本 - 精确解析 Excel 复制的 TSV 格式
 */

const fs = require('fs');

function convertToMarkdown(inputFile, outputFile) {
    console.log('开始转换...');
    
    // 读取文件
    const content = fs.readFileSync(inputFile, 'utf-8');
    const lines = content.split(/\r?\n/);
    
    console.log(`读取到 ${lines.length} 行数据`);
    
    // 解析表头
    const headers = lines[0].split('\t');
    console.log('表头:', headers);
    
    // 解析测试用例 - 更精确的方法
    const testCases = [];
    let i = 1;
    
    while (i < lines.length) {
        const line = lines[i];
        if (!line || line.trim() === '') {
            i++;
            continue;
        }
        
        const parts = line.split('\t');
        
        // 检查是否是新的测试用例（第一列是数字）
        if (parts[0] && /^\d+$/.test(parts[0].trim())) {
            const testCase = {
                id: parts[0].trim(),
                testPoint: parts[1] ? parts[1].trim() : '',
                testTarget: parts[2] ? parts[2].trim() : '',
                testSteps: parts[3] ? parts[3].trim() : '',
                verificationItems: parts[4] ? parts[4].trim() : ''
            };
            
            // 继续读取后续行，直到遇到下一个测试用例或文件结束
            i++;
            while (i < lines.length) {
                const nextLine = lines[i];
                if (!nextLine || nextLine.trim() === '') {
                    i++;
                    continue;
                }
                
                const nextParts = nextLine.split('\t');
                
                // 如果下一行是新的测试用例，停止
                if (nextParts[0] && /^\d+$/.test(nextParts[0].trim())) {
                    break;
                }
                
                // 否则，将内容添加到当前测试用例
                for (let j = 0; j < Math.min(nextParts.length, 5); j++) {
                    const content = nextParts[j] ? nextParts[j].trim() : '';
                    if (!content) continue;
                    
                    switch (j) {
                        case 1: // 試験観点
                            if (testCase.testPoint) testCase.testPoint += '\n' + content;
                            else testCase.testPoint = content;
                            break;
                        case 2: // 試験対象
                            if (testCase.testTarget) testCase.testTarget += '\n' + content;
                            else testCase.testTarget = content;
                            break;
                        case 3: // 試験手順
                            if (testCase.testSteps) testCase.testSteps += '\n' + content;
                            else testCase.testSteps = content;
                            break;
                        case 4: // 確認項目
                            if (testCase.verificationItems) testCase.verificationItems += '\n' + content;
                            else testCase.verificationItems = content;
                            break;
                    }
                }
                i++;
            }
            
            testCases.push(testCase);
            console.log(`解析测试用例 ${testCase.id}: ${testCase.testPoint.substring(0, 50)}...`);
        } else {
            i++;
        }
    }
    
    console.log(`解析完成，共 ${testCases.length} 个测试用例`);
    
    // 生成 Markdown
    let markdown = `# 测试用例文档 (2-4-1)\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    markdown += `## 目录\n\n`;
    
    // 生成目录
    testCases.forEach(testCase => {
        const title = testCase.testPoint.split('\n')[0].substring(0, 80);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${testCase.testPoint}\n\n`;
        
        markdown += `### 试验对象\n`;
        if (testCase.testTarget) {
            const targets = testCase.testTarget.split('\n').filter(t => t.trim());
            targets.forEach(target => {
                markdown += `- ${target}\n`;
            });
        }
        markdown += `\n`;
        
        markdown += `### 试验手順\n`;
        markdown += `${testCase.testSteps}\n\n`;
        
        markdown += `### 确认项目\n`;
        markdown += `${testCase.verificationItems}\n\n`;
        
        markdown += `---\n\n`;
    });
    
    // 写入文件
    fs.writeFileSync(outputFile, markdown, 'utf-8');
    
    console.log(`✅ 转换完成！输出文件: ${outputFile}`);
    console.log(`📊 文件大小: ${Math.round(fs.statSync(outputFile).size / 1024)} KB`);
}

// 运行转换
if (require.main === module) {
    const inputFile = process.argv[2] || '2-4-1.txt';
    const outputFile = process.argv[3] || '2-4-1-final.md';
    
    convertToMarkdown(inputFile, outputFile);
}
