{"name": "integration", "version": "1.0.0", "description": "jcs-endpoint-monorepo 的 Playwright 集成测试项目", "main": "index.js", "scripts": {"_comment1": "=== 自动启动模式（完整 webServer）===", "test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:ui": "npx playwright test --ui", "_comment2": "=== 调试模式（部分 webServer）===", "test:debug": "npx playwright test --config=playwright.config.debug.ts", "test:debug:headed": "npx playwright test --config=playwright.config.debug.ts --headed", "_comment3": "=== 通用工具 ===", "test:specific": "npx playwright test --grep", "show-report": "npx playwright show-report ../test-results/html-report", "show-trace": "npx playwright show-trace ../test-results/test-output", "clean-results": "rimraf ../test-results", "_comment4": "=== 构建检查 ===", "check-builds": "node scripts/check-builds.js", "check-builds:quiet": "node scripts/check-builds.js --quiet", "check-builds:json": "node scripts/check-builds.js --json", "_comment5": "=== Mock Server ===", "start-mock-server": "ts-node scripts/start-mock-server.ts", "mock-server:dev": "MOCK_SERVER_PORT=3001 ts-node scripts/start-mock-server.ts", "_comment6": "=== 脚本方式（兼容性）===", "e2e": "bash ./scripts/run-e2e-tests.sh", "e2e:headed": "bash ./scripts/run-e2e-tests.sh --headed", "e2e:debug": "bash ./scripts/run-e2e-tests.sh --debug", "_comment7": "=== Azurite 服务 ===", "start:azurite": "azurite --blobHost 0.0.0.0 --queueHost 0.0.0.0 --silent --location azurite-data --debug azurite-debug.log"}, "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.43.1", "@types/express": "^4.17.21", "@types/node": "^24.0.15", "azurite": "^3.31.0", "rimraf": "^5.0.1", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"@azure/storage-blob": "^12.28.0", "@azure/storage-file-share": "^12.28.0", "@prisma/client": "^6.12.0", "dotenv": "^17.2.0", "express": "^4.18.2", "iron-session": "^8.0.4"}}