# Playwright 配置文件说明

## 配置文件概览

项目中有两个不同的 Playwright 配置文件，每个都有特定的用途：

### 1. `playwright.config.ts` - 完整自动化配置（默认）

**用途**: 生产级集成测试，完全自动化
**特点**:
- ✅ 自动启动所有服务（webServer）
- ✅ Next.js + Azure Functions + Mock Server 全栈启动
- ✅ 完整的HTML报告和追踪
- ✅ 智能重试策略（CI: 3次，本地: 1次）
- ✅ 全局环境设置和清理
- ⏱️ 90秒测试超时（考虑服务冷启动）

**使用场景**:
- CI/CD 流水线
- 完整的端到端测试
- 自动化回归测试

**运行命令**:
```bash
npx playwright test  # 使用默认配置
```

### 2. `playwright.config.debug.ts` - 调试模式配置

**用途**: 开发调试，快速验证
**特点**:
- 🔧 部分使用 webServer（只自动启动 Next.js）
- 🔧 其他服务通过 TestServicesManager 在测试中按需启动
- 🔧 使用简化的全局设置（simple-global-setup.ts）
- 📝 只输出控制台日志，不生成HTML报告
- 🚀 单线程执行，避免端口冲突
- ❌ 不重试，快速失败
- ⏱️ 较长的超时时间（120秒）

**使用场景**:
- 开发过程中的快速调试
- 单个测试用例验证
- 问题排查

**运行命令**:
```bash
npx playwright test --config=playwright.config.debug.ts
```



## 配置对比表

| 特性 | 自动启动配置 | 调试模式配置 |
|------|-------------|-------------|
| webServer 自动启动 | ✅ 完整服务栈 | 🔧 仅 Next.js |
| 其他服务启动 | ✅ 自动启动 | 🔧 测试中按需启动 |
| HTML 报告 | ✅ | ❌ |
| 重试次数 | 1-3次 | 0次 |
| 并发数 | 2-4个 | 1个 |
| 测试超时 | 90秒 | 120秒 |
| 全局设置 | 简化版 | 简化版 |
| 适用场景 | 生产测试/CI | 开发调试 |

## 选择指南

### 什么时候使用自动启动配置？
- ✅ CI/CD 流水线
- ✅ 完整的回归测试
- ✅ 新功能验证
- ✅ 发布前测试
- ✅ 一键完整测试

### 什么时候使用调试模式配置？
- 🔧 开发过程中快速验证
- 🔧 单个测试用例调试
- 🔧 问题排查和分析
- 🔧 测试用例开发
- � 服务启动问题排查

## 常用命令示例

```bash
# 使用自动启动配置运行所有测试（默认）
npx playwright test

# 使用调试模式配置运行特定测试
npx playwright test --config=playwright.config.debug.ts --grep "应该验证服务状态"

# 查看测试报告
npx playwright show-report ../test-results/html-report
```

## 注意事项

1. **端口冲突**: 自动启动配置会自动管理所有端口，调试模式配置通过 TestServicesManager 智能管理端口
2. **服务依赖**: 调试模式配置会在测试中按需启动所需服务
3. **数据清理**: 两种配置都包含完整的数据清理逻辑
4. **报告位置**: 所有配置的报告都输出到 `../test-results/` 目录