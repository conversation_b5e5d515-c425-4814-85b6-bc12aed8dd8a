/**
 * @fileoverview タスク関連データ操作モジュール
 * @description
 * コンテナ同時実行状態確認：
 * - Azure VM名およびDockerコンテナ名を複合キーとして使用し、コンテナ実行状態（ContainerConcurrencyStatus）テーブルから対象コンテナのステータスを確認
 * - IDLE またはレコードが存在しない場合は処理を続行、BUSYの場合はタスク実行要求を拒否
 *
 * タスク一覧データ取得：
 * - 指定されたライセンスIDに紐づくタスク一覧をキャッシュ付きで取得
 * - フィルタリング、ソート、ページング処理をメモリ内で実行
 * - タスク種別・ステータスのLOV値をラベル変換して表示用データを構築
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { Prisma, Task } from "@prisma/client";
import { ServerDataLov } from "./lov";
import {
  ENV,
  TASK_STATUS_LOV,
  TASK_TYPE_LOV,
  PORTAL_CACHE_KEY_TASKS,
  TASK_STATUS,
} from "../definitions";

import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import { castValueToLabel } from "../utils";

/**
 * タスク関連のデータ操作を行う静的クラス
 *
 * タスクの並行性チェック、保持ポリシーに基づくタスク取得・削除、LOV値取得等を担当し、
 * タスク受付処理の各種データアクセス要件を満たす。
 */
export class ServerDataTasks {
  /**
   * 特定のコンテナの並行実行状態を取得
   *
   * コンテナ同時実行状態確認処理：
   * - Azure VM名Server.azureVmName および Dockerコンテナ名Server.dockerContainerName を複合キーとして使用
   * - コンテナ実行状態（ContainerConcurrencyStatus）テーブルから対象コンテナのステータスを確認
   * - IDLE またはレコードが存在しない場合は処理を続行、BUSYの場合はタスク実行要求を拒否
   *
   * @param {string} targetVmName - 対象VM名
   * @param {string} targetContainerName - 対象コンテナ名
   * @returns {Promise<string | null>} コンテナの状態（'BUSY', 'IDLE'等）、見つからない場合はnull
   * @throws DBアクセスエラー時はhandleServerErrorに委譲される
   */
  @LogFunctionSignature()
  static async getContainerStatus(
    targetVmName: string,
    targetContainerName: string,
  ) {
    try {
      const containerStatus =
        await prisma.containerConcurrencyStatus.findUnique({
          where: {
            targetVmName_targetContainerName: {
              targetVmName,
              targetContainerName,
            },
          },
          select: {
            status: true,
          },
        });
      return containerStatus?.status ?? null;
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * LOVから単一の値を取得し、見つからない場合はデフォルト値を返す。
   *
   * @template T
   * @param {string} code - 取得するLOVのコード。
   * @param {T} defaultValue - LOVが見つからない場合に返すデフォルト値。
   * @returns {Promise<T>} LOVの値またはデフォルト値。
   */
  @LogFunctionSignature()
  static async getLovValue<T>(code: string, defaultValue: T): Promise<T> {
    const lov = await ServerDataLov.fetchLov(code);
    if (lov && lov.value) {
      // Attempt to cast to the type of defaultValue
      return (
        typeof defaultValue === "number" ? Number(lov.value) : lov.value
      ) as T;
    }
    return defaultValue;
  }

  /**
   * 保持ポリシーに基づき、削除対象となる可能性のあるタスクを取得する。
   *
   * @param {Prisma.TransactionClient} tx - Prismaトランザクションクライアント。
   * @param {string} serverId - サーバーID。
   * @param {number} maxRetentionCount - 最大保持タスク数。
   * @returns {Promise<Task[]>} 削除対象のタスク一覧。
   */
  @LogFunctionSignature()
  static async getTasksForRetentionCheck(
    tx: Prisma.TransactionClient,
    serverId: string,
    maxRetentionCount: number,
  ): Promise<Task[]> {
    const completedStatuses = [
      TASK_STATUS.COMPLETED_SUCCESS,
      TASK_STATUS.COMPLETED_ERROR,
      TASK_STATUS.CANCELLED,
    ];

    const completedTaskCount = await tx.task.count({
      where: {
        targetServerId: serverId,
        status: { in: completedStatuses },
      },
    });

    // 削除対象タスク数を計算する。新規タスク分の空きを確保するため(maxRetentionCount-1)を引く。
    const tasksToDeleteCount = completedTaskCount - (maxRetentionCount - 1);

    if (tasksToDeleteCount <= 0) {
      return [];
    }

    const tasksToDelete = await tx.task.findMany({
      where: {
        targetServerId: serverId,
        status: { in: completedStatuses },
      },
      orderBy: {
        submittedAt: "asc",
      },
      take: tasksToDeleteCount,
    });

    return tasksToDelete;
  }

  /**
   * トランザクション内で、タスクとそれに関連するリソースを削除する。
   *
   * @param {Prisma.TransactionClient} tx - Prismaトランザクションクライアント。
   * @param {Task} task - 削除するタスクオブジェクト。
   */
  @LogFunctionSignature()
  static async deleteTaskAndAssociatedResourcesInTx(
    tx: Prisma.TransactionClient,
    task: Task,
  ) {
    // TODO: 関連するBlobやログを削除するロジックを実装します。
    await tx.task.delete({ where: { id: task.id } });
  }

  /**
   * トランザクション内で、新しいタスクレコードを作成する。
   *
   * @param {Prisma.TransactionClient} tx - Prismaトランザクションクライアント。
   * @param {any} taskData - 新規タスクのデータ。
   */
  @LogFunctionSignature()
  static async createTaskInDbInTx(tx: Prisma.TransactionClient, taskData: any) {
    await tx.task.create({ data: taskData });
  }

  /**
   * 指定されたライセンスIDに紐づくタスク一覧をキャッシュ付きで取得します。
   * @param {string} licenseId - ユーザーのライセンスID。
   * @returns {Promise<any[]>} タスク情報の配列。
   */
  static fetchCachedTasks(licenseId: string) {
    // unstable_cache を関数内で呼び出すパターンに変更します
    const cacheTag = `${PORTAL_CACHE_KEY_TASKS}-${licenseId}`;

    const cachedFn = unstable_cache(
      async () => {
        const [tasks, taskStatus, taskType] = await Promise.all([
          prisma.task.findMany({ where: { licenseId } }),
          prisma.lov.findMany({ where: { parentCode: TASK_STATUS_LOV } }),
          prisma.lov.findMany({ where: { parentCode: TASK_TYPE_LOV } }),
        ]);

        return tasks.map((task) => ({
          ...task,
          taskTypeLabel: castValueToLabel(task.taskType, taskType) || "",
          statusLabel: castValueToLabel(task.status, taskStatus) || "",
        }));
      },
      // キーパーツにも動的な値を含めることができます
      [PORTAL_CACHE_KEY_TASKS, licenseId],
      {
        // tags プロパティに revalidateTag で使うタグを正確に設定します
        tags: [cacheTag],
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
      },
    );

    return cachedFn();
  }

  /**
   * フィルタリング後のタスク一覧の総ページ数を計算します。
   * @param {string} filter - フィルタリングキーワード。
   * @param {number} size - 1ページあたりの表示件数。
   * @param {boolean} refresh - キャッシュを強制的にリフレッシュするかどうか。
   * @returns {Promise<number>} 総ページ数。
   */
  @LogFunctionSignature()
  static async fetchTasksPages(filter: string, size: number, refresh: boolean) {
    try {
      // 現在のユーザーセッションを取得します。
      const session = await getIronSession<SessionData>(
        cookies(),
        sessionOptions,
      );
      const licenseId = session!.user.licenseId;

      // refreshフラグがtrueの場合、関連するキャッシュタグを無効化します。
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_TASKS}-${licenseId}`);
      }

      // キャッシュされたタスクデータを取得します。
      const cachedTasks = await this.fetchCachedTasks(licenseId);

      if (cachedTasks) {
        // フィルタリングロジック
        const filteredTasks = !filter
          ? cachedTasks
          : cachedTasks.filter((task) => {
            const searchableValues = [
              task.taskName,
              task.status,
              task.targetServerName,
              task.taskType,
              task.submittedByUserId,
              task.startedAt,
              task.endedAt,
            ];
            return searchableValues
              .filter((v) => v != null && v !== "")
              .some((v) =>
                v!.toString().toLowerCase().includes(filter.toLowerCase()),
              );
          });

        // フィルタリング後の件数とページサイズから総ページ数を計算します。
        return Math.ceil(filteredTasks.length / size);
      }

      return 0;
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ、ソート、ページングを適用したタスク一覧を取得します。
   * @param {string} filter - フィルタ文字列。
   * @param {number} size - 1ページあたりの件数。
   * @param {number} page - ページ番号。
   * @param {"taskName" | ...} sort - ソートキー。
   * @param {"asc" | "desc"} order - ソート順。
   * @returns {Promise<any[]>} 表示用のタスク情報配列。
   */
  @LogFunctionSignature()
  static async fetchFilteredTasks(
    filter: string,
    size: number,
    page: number,
    sort:
      | "taskName"
      | "status"
      | "startedAt"
      | "endedAt"
      | "targetServerName"
      | "taskType"
      | "submittedByUserId",
    order: "asc" | "desc",
  ) {
    try {
      // ユーザーセッションからライセンスIDを取得し、対応するタスクデータを取得します。
      const session = await getIronSession<SessionData>(
        cookies(),
        sessionOptions,
      );
      const cachedTasks = await this.fetchCachedTasks(session!.user.licenseId);

      if (cachedTasks) {
        // ステップ1: フィルタリング
        let filteredTasks = cachedTasks;
        if (filter) {
          const lowerCaseFilter = filter.toLowerCase();
          filteredTasks = cachedTasks.filter((task) => {
            // 検索対象となるフィールドの値を配列にまとめます。
            const searchableValues = [
              task.taskName,
              task.status,
              task.targetServerName,
              task.taskType,
              task.submittedByUserId,
              task.startedAt,
              task.endedAt,
            ];
            // いずれかのフィールドがフィルタ文字列を含んでいれば、そのタスクを結果に含めます。
            return searchableValues
              .filter((v) => v != null && v !== "")
              .some((v) =>
                v!.toString().toLowerCase().includes(lowerCaseFilter),
              );
          });
        }

        // ステップ2: ソート
        if (sort) {
          filteredTasks.sort((a, b) => {
            let comparison = 0;
            const isDateSort = sort === "startedAt" || sort === "endedAt";

            // 日付フィールドのソート処理
            if (isDateSort) {
              // ヘルパー関数: Dateオブジェクト、日付文字列、nullなど、様々な入力を正規化してDateオブジェクトまたはnullを返します。
              const getDateObject = (value: any): Date | null => {
                if (value instanceof Date) return value;
                if (typeof value === "string" && value) {
                  const date = new Date(value);
                  return isNaN(date.getTime()) ? null : date;
                }
                return null;
              };
              const aDate = getDateObject(a[sort]);
              const bDate = getDateObject(b[sort]);

              // 昇順・降順とnull値の扱いを定義します。
              if (order === "asc") {
                // 昇順の場合、nullは先頭に来ます。
                if (aDate === null && bDate === null) comparison = 0;
                else if (aDate === null) comparison = -1;
                else if (bDate === null) comparison = 1;
                else comparison = aDate.getTime() - bDate.getTime();
              } else {
                // 降順の場合、nullは末尾に来ます。
                if (aDate === null && bDate === null) comparison = 0;
                else if (aDate === null) comparison = 1;
                else if (bDate === null) comparison = -1;
                else comparison = bDate.getTime() - aDate.getTime();
              }
            } else {
              // 文字列フィールドのソート処理
              const aValue = (a[sort] ?? "").toString().toLowerCase();
              const bValue = (b[sort] ?? "").toString().toLowerCase();
              comparison =
                order === "asc"
                  ? aValue.localeCompare(bValue)
                  : bValue.localeCompare(aValue);
            }

            // 第二ソートキーによる安定ソート
            // 第一ソートキーで同値だった場合、taskNameの昇順で追加ソートします。
            if (comparison === 0 && sort !== "taskName") {
              const aTaskName = (a.taskName ?? "").toLowerCase();
              const bTaskName = (b.taskName ?? "").toLowerCase();
              return aTaskName.localeCompare(bTaskName);
            }
            return comparison;
          });
        }

        // ステップ3: ページング
        // ソート済みの配列から、指定されたページに該当する部分を切り出して返します。
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        return filteredTasks.slice(startIndex, endIndex);
      }

      return [];
    } catch (error) {
      handleServerError(error);
    }
  }
}
