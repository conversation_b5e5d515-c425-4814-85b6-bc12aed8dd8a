/**
 * 修复版本的转换脚本 - 正确处理第5列不带引号的确认项目
 */

const fs = require('fs');

function convertToMarkdown(inputFile, outputFile) {
    console.log('开始修复版本转换...');
    
    // 读取整个文件内容
    const content = fs.readFileSync(inputFile, 'utf-8');
    
    // 按测试用例分割（以数字+tab开头的行）
    const testCaseBlocks = content.split(/\n(?=\d+\t)/);
    
    // 移除表头
    testCaseBlocks.shift();
    
    console.log(`找到 ${testCaseBlocks.length} 个测试用例块`);
    
    const testCases = [];
    
    testCaseBlocks.forEach((block, index) => {
        try {
            const testCase = parseTestCaseBlock(block.trim());
            if (testCase) {
                testCases.push(testCase);
                console.log(`解析测试用例 ${testCase.id}: ${testCase.testPoint.substring(0, 50)}...`);
            }
        } catch (error) {
            console.warn(`解析测试用例 ${index + 1} 时出错:`, error.message);
        }
    });
    
    console.log(`成功解析 ${testCases.length} 个测试用例`);
    
    // 生成 Markdown
    let markdown = `# 测试用例文档 (2-4-1)\n\n`;
    markdown += `> 从 Excel 文件转换生成，共 ${testCases.length} 个测试用例\n\n`;
    markdown += `## 目录\n\n`;
    
    // 生成目录
    testCases.forEach(testCase => {
        const title = testCase.testPoint.substring(0, 80);
        markdown += `- [${testCase.id}. ${title}](#测试用例-${testCase.id})\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // 生成每个测试用例
    testCases.forEach(testCase => {
        markdown += `## 测试用例 ${testCase.id}\n\n`;
        
        markdown += `### 试验观点\n`;
        markdown += `${testCase.testPoint}\n\n`;
        
        markdown += `### 试验对象\n`;
        if (testCase.testTarget) {
            const targets = testCase.testTarget.split('\n').filter(t => t.trim());
            targets.forEach(target => {
                markdown += `- ${target.trim()}\n`;
            });
        }
        markdown += `\n`;
        
        markdown += `### 试验手順\n`;
        if (testCase.testSteps) {
            markdown += `${testCase.testSteps}\n\n`;
        } else {
            markdown += `（无内容）\n\n`;
        }
        
        markdown += `### 确认项目\n`;
        if (testCase.verificationItems) {
            markdown += `${testCase.verificationItems}\n\n`;
        } else {
            markdown += `（无内容）\n\n`;
        }
        
        markdown += `---\n\n`;
    });
    
    // 写入文件
    fs.writeFileSync(outputFile, markdown, 'utf-8');
    
    console.log(`✅ 转换完成！输出文件: ${outputFile}`);
    console.log(`📊 文件大小: ${Math.round(fs.statSync(outputFile).size / 1024)} KB`);
}

/**
 * 解析单个测试用例块 - 修复版本
 * 格式：数字 TAB 试验观点 TAB "试验对象" TAB "试验手順" TAB 确认项目（不带引号）
 */
function parseTestCaseBlock(block) {
    if (!block) return null;
    
    // 先提取ID和试验观点
    const firstTabIndex = block.indexOf('\t');
    if (firstTabIndex === -1) return null;
    
    const id = block.substring(0, firstTabIndex).trim();
    if (!/^\d+$/.test(id)) return null;
    
    let remaining = block.substring(firstTabIndex + 1);
    
    // 找到试验观点（到第一个引号前的内容）
    const firstQuoteIndex = remaining.indexOf('"');
    if (firstQuoteIndex === -1) return null;
    
    const testPoint = remaining.substring(0, firstQuoteIndex).replace(/\t$/, '').trim();
    remaining = remaining.substring(firstQuoteIndex);
    
    // 解析两个引号字段（试验对象和试验手順）
    const quotedFields = [];
    let currentPos = 0;
    
    for (let i = 0; i < 2; i++) {
        if (currentPos >= remaining.length) break;
        
        // 找到开始引号
        const startQuote = remaining.indexOf('"', currentPos);
        if (startQuote === -1) break;
        
        // 找到结束引号（考虑到可能有多行）
        let endQuote = startQuote + 1;
        let foundEnd = false;
        
        while (endQuote < remaining.length && !foundEnd) {
            if (remaining[endQuote] === '"') {
                // 检查是否是真正的结束（后面是tab、换行或文件结束）
                const nextChar = remaining[endQuote + 1];
                if (!nextChar || nextChar === '\t' || nextChar === '\n') {
                    foundEnd = true;
                } else {
                    endQuote++;
                }
            } else {
                endQuote++;
            }
        }
        
        if (foundEnd) {
            const fieldContent = remaining.substring(startQuote + 1, endQuote);
            quotedFields.push(fieldContent);
            currentPos = endQuote + 1;
            
            // 跳过可能的tab
            if (remaining[currentPos] === '\t') {
                currentPos++;
            }
        } else {
            break;
        }
    }
    
    // 解析第5列确认项目（不带引号的文本）
    let verificationItems = '';
    if (currentPos < remaining.length) {
        // 从当前位置到结束的所有内容都是确认项目
        verificationItems = remaining.substring(currentPos).trim();
    }
    
    return {
        id: id,
        testPoint: testPoint,
        testTarget: quotedFields[0] || '',
        testSteps: quotedFields[1] || '',
        verificationItems: verificationItems
    };
}

// 运行转换
if (require.main === module) {
    const inputFile = process.argv[2] || '2-4-1.txt';
    const outputFile = process.argv[3] || '2-4-1-fixed.md';
    
    convertToMarkdown(inputFile, outputFile);
}
