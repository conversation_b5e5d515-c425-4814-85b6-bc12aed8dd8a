/**
 * @fileoverview 管理項目エクスポートテスト用ヘルパー関数
 * @description
 * 管理項目エクスポート機能のテストで共通利用される処理を提供する。
 * 正常系・異常系テストケースで再利用可能な関数群を含む。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { Page, expect } from '@playwright/test';
import { PrismaClient } from '@prisma/client';
import { MockServerHelper } from './mock-server-helper';
import { MockJobStatus } from './azure-automation-mock-server';

/**
 * 管理項目エクスポートタスクの実行結果
 */
export interface MgmtExportTaskResult {
  /** 作成されたタスクID */
  taskId: string;
  /** 最終的なタスクステータス */
  finalStatus: string;
  /** エラー詳細（失敗時のみ） */
  errorDetails?: {
    errorCode?: string;
    errorMessage?: string;
    resultMessage?: string;
    taskName?: string;
  };
}

/**
 * 管理項目エクスポートタスクの実行オプション
 */
export interface MgmtExportTaskOptions {
  /** 期待するタスクステータス（デフォルト: COMPLETED_SUCCESS） */
  expectedStatus?: string;
  /** タスク完了待機のタイムアウト時間（ミリ秒、デフォルト: 90000） */
  timeout?: number;
  /** 作業ステータス設定を無効化するかどうか（デフォルト: false） */
  disableJobStatusSetting?: boolean;
}

/**
 * サーバ一覧ページに移動し、ページが正常に読み込まれることを確認する
 * 
 * @param page - Playwrightページオブジェクト
 * @param testServerName - 確認対象のテストサーバ名
 */
export async function navigateToServersPage(page: Page, testServerName: string): Promise<void> {
  console.log('📝 サーバ一覧ページへ移動');
  await page.goto('/dashboard/servers');
  await page.waitForLoadState('networkidle');
  
  // ページ読み込み成功の確認
  await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
  await expect(page.getByText(testServerName)).toBeVisible();
  console.log('✅ サーバ一覧ページ読み込み成功');
}

/**
 * 管理項目エクスポートタスクを実行する
 * 
 * @param page - Playwrightページオブジェクト
 * @param testServerName - 対象サーバ名
 * @returns 作成されたタスクのデータベースレコード
 */
export async function executeMgmtExportTask(
  page: Page,
  testServerName: string,
  prisma: PrismaClient,
  licenseId: string
): Promise<{ id: string; status: string; taskName: string | null }> {
  console.log('📝 管理項目エクスポートタスクを実行');
  
  const serverRow = page.locator(`tr:has-text("${testServerName}")`);
  const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');
  
  await expect(taskSelectButton).toBeVisible();
  await taskSelectButton.click();
  
  const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
  await expect(exportMenuItem).toBeVisible();
  await exportMenuItem.click();
  
  // 確認ダイアログの処理
  const confirmDialog = page.locator('[role="dialog"], .modal, [data-modal]');
  await expect(confirmDialog).toBeVisible();
  
  const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行")');
  await expect(confirmButton.first()).toBeVisible();
  await confirmButton.first().click();
  
  // 成功メッセージの確認
  const successMessage = page.locator('text=/タスクの実行を受け付けました/');
  await expect(successMessage).toBeVisible();
  console.log('✅ エクスポートタスク作成成功');
  
  // データベースでタスク作成を確認
  await page.waitForTimeout(2000);
  
  const createdTask = await prisma.task.findFirst({
    where: {
      taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
      licenseId: licenseId,
      targetServerName: testServerName
    },
    orderBy: { submittedAt: 'desc' }
  });
  
  if (!createdTask) {
    throw new Error('タスクがデータベースに作成されませんでした');
  }
  
  console.log(`✅ タスク作成確認: ${createdTask.id}`);
  console.log(`📊 初期ステータス: ${createdTask.status}`);
  
  return createdTask;
}

/**
 * タスクの完了を待機し、最終ステータスを取得する
 * 
 * @param taskId - 監視対象のタスクID
 * @param prisma - Prismaクライアント
 * @param mockServerHelper - モックサーバヘルパー
 * @param options - 実行オプション
 * @returns タスクの実行結果
 */
export async function waitForTaskCompletion(
  taskId: string,
  prisma: PrismaClient,
  mockServerHelper: MockServerHelper,
  options: MgmtExportTaskOptions = {}
): Promise<MgmtExportTaskResult> {
  const {
    expectedStatus = 'TASK_STATUS.COMPLETED_SUCCESS',
    timeout = 90000,
    disableJobStatusSetting = false
  } = options;
  
  console.log('📝 タスク完了を待機');
  
  let finalTaskStatus: string | undefined;
  let taskErrorDetails: any = {};
  let defectAlreadyLogged = false;
  let jobStatusSet = false;

  await expect.poll(async () => {
    const task = await prisma.task.findUnique({
      where: { id: taskId }
    });
    console.log(`📊 現在のタスクステータス: ${task?.status}`);

    finalTaskStatus = task?.status;

    // RUNBOOK_SUBMITTED状態でモック作業ステータスを設定
    if (task?.status === 'TASK_STATUS.RUNBOOK_SUBMITTED' && !jobStatusSet && !disableJobStatusSetting) {
      try {
        console.log(`⏳ 自動ステータス変更完了を待機...`);
        await new Promise(resolve => setTimeout(resolve, 1000));

        await mockServerHelper.setJobStatus(task.id, MockJobStatus.Completed);
        console.log(`✅ 作業ステータスをCompletedに設定: ${task.id}`);
        jobStatusSet = true;
      } catch (error) {
        console.warn(`⚠️ 作業ステータス設定失敗:`, error);
      }
    }

    // エラー時の詳細情報記録（一度のみ）
    if (task?.status === 'TASK_STATUS.COMPLETED_ERROR' && !defectAlreadyLogged) {
      taskErrorDetails = {
        errorCode: task.errorCode,
        errorMessage: task.errorMessage,
        resultMessage: task.resultMessage,
        taskName: task.taskName
      };
      console.log(`❌ タスク失敗詳細:`);
      console.log(`   エラーコード: ${task.errorCode}`);
      console.log(`   エラーメッセージ: ${task.errorMessage}`);
      console.log(`   結果メッセージ: ${task.resultMessage}`);
      console.log(`   タスク名: ${task.taskName}`);

      defectAlreadyLogged = true;
    }

    // 完了状態（成功または失敗）で轮询停止
    if (task?.status === 'TASK_STATUS.COMPLETED_SUCCESS' ||
        task?.status === 'TASK_STATUS.COMPLETED_ERROR') {
      return task.status;
    }

    return task?.status;
  }, {
    message: 'タスク完了待機（RunbookMonitorFunc処理含む）',
    timeout: timeout,
    intervals: [5000, 10000, 15000]
  }).toBe(expectedStatus);

  const result: MgmtExportTaskResult = {
    taskId,
    finalStatus: finalTaskStatus!,
    errorDetails: Object.keys(taskErrorDetails).length > 0 ? taskErrorDetails : undefined
  };

  if (finalTaskStatus === expectedStatus) {
    console.log(`✅ タスク完了: ${expectedStatus}`);
  } else {
    console.log(`❌ タスク失敗: 期待=${expectedStatus}, 実際=${finalTaskStatus}`);
  }

  return result;
}

/**
 * Azure Functions がタスクを処理したことを確認する
 * 
 * @param taskId - 確認対象のタスクID
 * @param servicesManager - サービス管理オブジェクト
 */
export async function verifyAzureFunctionsProcessing(taskId: string, servicesManager: any): Promise<void> {
  console.log('📝 Azure Functions処理確認');
  
  const standardFunctionsLogs = servicesManager.getServiceLogs('standard-functions');
  const longRunningFunctionsLogs = servicesManager.getServiceLogs('long-running-functions');
  
  const allLogs = [...standardFunctionsLogs, ...longRunningFunctionsLogs].join('\n');
  
  expect(allLogs).toContain(taskId);
  console.log('✅ Azure Functions処理確認完了');
}

/**
 * 完全な管理項目エクスポートフローを実行する（正常系）
 * 
 * @param page - Playwrightページオブジェクト
 * @param testServerName - 対象サーバ名
 * @param licenseId - ライセンスID
 * @param prisma - Prismaクライアント
 * @param mockServerHelper - モックサーバヘルパー
 * @param servicesManager - サービス管理オブジェクト
 * @returns タスクの実行結果
 */
export async function executeCompleteMgmtExportFlow(
  page: Page,
  testServerName: string,
  licenseId: string,
  prisma: PrismaClient,
  mockServerHelper: MockServerHelper,
  servicesManager: any
): Promise<MgmtExportTaskResult> {
  // ステップ1: サーバ一覧ページへ移動
  await navigateToServersPage(page, testServerName);
  
  // ステップ2: エクスポートタスク実行
  const createdTask = await executeMgmtExportTask(page, testServerName, prisma, licenseId);
  
  // ステップ3: タスク完了待機
  const result = await waitForTaskCompletion(createdTask.id, prisma, mockServerHelper);
  
  // ステップ4: Azure Functions処理確認
  await verifyAzureFunctionsProcessing(createdTask.id, servicesManager);
  
  console.log('📋 完全な管理項目エクスポートフロー完了');
  return result;
}
