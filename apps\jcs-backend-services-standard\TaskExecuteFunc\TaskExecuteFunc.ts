/**
 * @fileoverview タスク実行関数 (TaskExecuteFunc)
 * @description
 * Azure Service BusのTaskInputQueueからバックグラウンドタスク実行要求メッセージを受信し、
 * 早期失敗機構によるコンテナロック、タスクの前処理（検証、リソース確保）を行い、
 * Azure AutomationのRunbookジョブを起動する関数。効率的なリソース管理と競合制御を実現する。
 *
 * @trigger Azure Service Bus - TaskInputQueue キューメッセージ
 * @input TaskInputQueue から受信するJSON形式のメッセージ（{ taskId: 文字列 }）
 * @output Azure SQL Database (Task、ContainerConcurrencyStatus) のレコード更新、
 *         Azure Files上のタスク作業ディレクトリ作成、Azure AutomationへのRunbookジョブ作成要求
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext } from "@azure/functions";
import { prisma } from "../lib/prisma";
import {
  createShareServiceClient,
  createAutomationJob,
  createBlobServiceClient,
} from "../lib/azureClients";
import { cleanupOldTasks } from "../lib/cleanup";
import { AppConstants } from "../lib/constants";
import {
  formatTaskErrorMessage,
  isPrismaError,
  isAzureFilesError,
  isAzureBlobError,
  deleteTaskWorkspaceDirectory,
  logErrorWithStack
} from "../lib/utils";

/**
 * タスクをエラー状態に更新する補助関数
 *
 * エラー処理メカニズムの各エラーシナリオに対応し、Taskテーブルの該当タスクのステータスを
 * COMPLETED_ERRORに、errorCodeを指定されたエラーコードに、タスク詳細を対応するメッセージに更新する。
 * 対象エラーコード: EMET0001（コンテナビジー状態）、EMET0002（Azure Files作業ディレクトリ作成失敗）、
 * EMET0003（Blobからのファイルダウンロード失敗）、EMET0007（データベース更新失敗）、EMET0008（予期せぬ内部エラー）、
 * EMET0009（タスク実行時確認エラー）
 */
async function updateTaskToError(
  taskId: string,
  errorCode: string,
  resultMessage: string,
  originalUpdatedAt: Date,
  systemError?: Error
) {
  const updateResult = await prisma.task.updateMany({
    where: {
      id: taskId,
      updatedAt: originalUpdatedAt // 楽観ロック制御
    },
    data: {
      status: AppConstants.TaskStatus.CompletedError,
      errorCode,
      resultMessage,
      errorMessage: systemError?.message,
    },
  });

  // エラー処理としてのDB更新にも失敗/更新件数が0件の場合は例外をthrowしてリトライ
  if (updateResult.count === 0) {
    throw new Error(`タスク${taskId}のエラー更新失敗: 楽観ロック失敗またはタスクが存在しない`);
  }
}

/**
 * Azure Automation API呼び出し失敗時のタスクエラー更新補助関数
 *
 * ステップ12でAzure Automation Runbookジョブ作成API呼び出しが失敗した場合に使用。
 * この時点でタスクステータスは既にRUNBOOK_SUBMITTEDに更新されているため、
 * 更新条件にステータス=RUNBOOK_SUBMITTEDを含める。
 */
async function updateTaskToErrorAfterRunbookSubmitted(
  taskId: string,
  errorCode: string,
  resultMessage: string,
  systemError?: Error
) {
  const updateResult = await prisma.task.updateMany({
    where: {
      id: taskId,
      status: AppConstants.TaskStatus.RunbookSubmitted // RUNBOOK_SUBMITTED状態のタスクを対象
    },
    data: {
      status: AppConstants.TaskStatus.CompletedError,
      errorCode,
      resultMessage,
      errorMessage: systemError?.message,
    },
  });

  // エラー処理としてのDB更新にも失敗/更新件数が0件の場合は例外をthrowしてリトライ
  if (updateResult.count === 0) {
    throw new Error(`タスク${taskId}のエラー更新失敗: タスクが存在しないかステータスがRUNBOOK_SUBMITTEDではない`);
  }
}

/**
 * TaskExecuteFunc - バックグラウンドタスクの実行開始処理（早期失敗機構対応）
 *
 * 処理ステップ:
 * 1-2. メッセージ受信・解析、taskId検証
 * 3. タスク情報取得
 * 4. タスクステータス確認（QUEUED）
 * 5. コンテナ実行状態取得
 * 6. コンテナステータス確認（IDLE）
 * 7. コンテナステータス更新（BUSY、楽観ロック制御）
 * 8. Azure Files作業ディレクトリ作成
 * 9. 管理項目定義インポートファイル処理（該当タスクのみ）
 * 10. タスクステータス更新（RUNBOOK_SUBMITTED）
 * 11. REST APIリクエスト構築
 * 12. Azure Automation呼出
 * 13-14. クリーンアップ処理・正常終了
 *
 * エラー処理:
 * 早期失敗機構による効率的なエラー検出、適切なエラーコード設定（EMET0001/EMET0007）、
 * 補償処理（作業ディレクトリ削除、Runbookジョブ停止、コンテナロック解除）を実施。
 */
export async function TaskExecuteFunc(
  message: unknown,
  context: InvocationContext
): Promise<void> {
  // 補償処理とエラー処理で参照する変数を関数スコープで宣言
  let taskId: string | undefined;
  let task: any = null;
  let containerStatus: any = null;
  let workspaceCreated = false;
  let originalTaskUpdatedAt: Date | null = null;

  try {
    // 1. Azure Service Bus の TaskInputQueue からタスクメッセージを受信し、JSON形式から解析する
    // 受信したメッセージ内容（特にtaskId）をログに出力する
    context.log("[TaskExecuteFunc] TaskInputQueue からメッセージ受信");

    // メッセージ基本校验
    if (!message || typeof message !== 'object') {
      context.error("[TaskExecuteFunc] メッセージが不正です。処理を終了します。");
      return;
    }

    // 2. メッセージ内の基本パラメータ（taskId）の存在と値が空であるかを確認する
    const messageBody = message as { taskId?: string };
    const extractedTaskId = messageBody.taskId;

    if (!extractedTaskId || typeof extractedTaskId !== "string") {
      context.error("[TaskExecuteFunc] taskIdが不足/不正です。処理を終了します。");
      return;
    }

    taskId = extractedTaskId;
    context.log(`[TaskExecuteFunc] 受信taskId: ${taskId}`);

    // 3. taskId を使用して タスクTaskテーブルを検索し、タスクの情報及びタスクレコードの最終更新日時を取得する
    task = await prisma.task.findUnique({ where: { id: taskId } });
    if (!task) {
      // 取得失敗の場合：エラー詳細をログに出力して、処理を終了する
      context.error(`[TaskExecuteFunc] タスクID ${taskId} がデータベースに存在しません。処理を終了します。`);
      return;
    }

    // 楽観ロック制御のため、タスクレコードの最終更新日時を保存
    originalTaskUpdatedAt = task.updatedAt;
    context.log(`[TaskExecuteFunc] タスク情報取得成功: ${taskId}`);

    // 取得した情報が不足/不正の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、
    // errorCodeをEMET0009に、タスク詳細（resultMessage）を対応するメッセージに更新する
    if (
      !task.taskType ||
      !task.targetServerId ||
      !task.targetServerName ||
      !task.targetVmName ||
      !task.targetContainerName ||
      !task.targetHRWGroupName
    ) {
      context.error(`[TaskExecuteFunc] タスクID ${taskId} の構成情報が不足しています`);
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0009,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009),
        originalTaskUpdatedAt!
      );
      return;
    }

    // 必要な環境変数の事前チェック
    // 取得した情報が不足/不正の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、
    // errorCodeをEMET0009に、タスク詳細（resultMessage）を対応するメッセージに更新する
    const requiredEnvVars: { [key: string]: string | undefined } = {};

    // タスク種別に応じた Runbook 環境変数をチェック
    switch (task.taskType) {
      case AppConstants.TaskType.MgmtItemImport:
        requiredEnvVars['RUNBOOK_MGMT_ITEM_IMPORT'] = process.env.RUNBOOK_MGMT_ITEM_IMPORT;
        requiredEnvVars['AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF'] = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF;
        break;
      case AppConstants.TaskType.MgmtItemExport:
        requiredEnvVars['RUNBOOK_MGMT_ITEM_EXPORT'] = process.env.RUNBOOK_MGMT_ITEM_EXPORT;
        break;
      case AppConstants.TaskType.OpLogExport:
        requiredEnvVars['RUNBOOK_OPLOG_EXPORT'] = process.env.RUNBOOK_OPLOG_EXPORT;
        break;
      default:
        context.error(`[TaskExecuteFunc] 未定義のタスクタイプ: ${task.taskType}`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0009,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009),
          originalTaskUpdatedAt!
        );
        return;
    }

    // 環境変数の存在チェック
    for (const [envName, envValue] of Object.entries(requiredEnvVars)) {
      if (!envValue) {
        context.error(`[TaskExecuteFunc] 必要な環境変数が未設定です: ${envName}`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0009,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009),
          originalTaskUpdatedAt!
        );
        return;
      }
    }

    // 4. タスクのステータスがQUEUEDであるかチェックする。QUEUEDなら処理を続行する
    if (task.status !== AppConstants.TaskStatus.Queued) {
      // QUEUEDでない場合：処理対象外のため、エラー詳細をログに出力して、処理を終了する
      context.warn(
        `[TaskExecuteFunc] タスクID ${taskId} のステータスがQUEUEDではない（現状: ${task.status}）。処理対象外。`
      );
      return;
    }

    // 5. 対象VM名（Task.targetVmName）と対象コンテナ名（Task.targetContainerName）を複合キーとして、
    // コンテナ実行状態ContainerConcurrencyStatus テーブルを検索し、対象コンテナの現在のステータスと最終更新日時を取得する
    try {
      containerStatus = await prisma.containerConcurrencyStatus.findUnique({
        where: {
          targetVmName_targetContainerName: {
            targetVmName: task.targetVmName!,
            targetContainerName: task.targetContainerName!,
          },
        },
      });

      if (!containerStatus) {
        // 取得失敗の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、
        // タスク詳細（resultMessage）を対応するメッセージに更新する
        context.error(`[TaskExecuteFunc] コンテナ実行状態が見つかりません: VM=${task.targetVmName}, Container=${task.targetContainerName}`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0007,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0007),
          originalTaskUpdatedAt!
        );
        return;
      }

      // 取得した情報が不足/不正の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0009に、
      // タスク詳細（resultMessage）を対応するメッセージに更新する
      if (!containerStatus.status) {
        context.error(`[TaskExecuteFunc] コンテナ実行状態の構成情報が不足しています: VM=${task.targetVmName}, Container=${task.targetContainerName}`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0009,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009),
          originalTaskUpdatedAt!
        );
        return;
      }
    } catch (err: any) {
      // 取得失敗の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、
      // タスク詳細（resultMessage）を対応するメッセージに更新する
      logErrorWithStack(context, `[TaskExecuteFunc] コンテナ実行状態取得失敗:`, err);
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0007,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0007),
        originalTaskUpdatedAt!
      );
      return;
    }

    // 6. コンテナのステータスがIDLEであるかチェックする。IDLEなら処理を続行する
    if (containerStatus.status === "BUSY") {
      // BUSY の場合（対象コンテナが先に他のタスクに占有された）：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、
      // errorCodeをEMET0001に、タスク詳細（resultMessage）を対応するメッセージに更新する
      context.error(`[TaskExecuteFunc] コンテナが他のタスクに占有されています: ${task.targetServerName}`);
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0001,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0001, [task.targetServerName]),
        originalTaskUpdatedAt!
      );
      return;
    }

    // 7. コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'BUSY' に、
    // 使用中のタスクID（currentTaskId） を入力の taskId に更新する
    // 条件：対象VM名と対象コンテナ名が3.で取得した情報と一致し、最終更新日時がステップ5.で取得した日時と一致する（楽観ロック制御）
    try {
      const updateContainerResult = await prisma.containerConcurrencyStatus.updateMany({
        where: {
          targetVmName: task.targetVmName,
          targetContainerName: task.targetContainerName,
          updatedAt: containerStatus.updatedAt, // 楽観ロック条件
        },
        data: {
          status: "BUSY",
          currentTaskId: taskId,
        },
      });

      if (updateContainerResult.count === 0) {
        // 更新した件数が0件の場合（対象コンテナが先に他のタスクに占有された）：
        // Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0001に、
        // タスク詳細（resultMessage）を対応するメッセージに更新する
        context.error(`[TaskExecuteFunc] コンテナが他のタスクに占有されました: ${task.targetServerName}`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0001,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0001, [task.targetServerName]),
          originalTaskUpdatedAt!
        );
        return;
      }

      context.log(`[TaskExecuteFunc] コンテナステータスをBUSYに更新完了: VM=${task.targetVmName}, Container=${task.targetContainerName}`);
    } catch (err: any) {
      // DB更新失敗の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、
      // タスク詳細（resultMessage）を対応するメッセージに更新する
      logErrorWithStack(context, `[TaskExecuteFunc] コンテナ実行状態更新失敗:`, err);
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0007,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0007),
        originalTaskUpdatedAt!
      );
      return;
    }

    // 8. Azure Files共有ストレージ上に、現在の taskId を用いたタスク固有の一時作業ディレクトリ TaskWorkspaces/<taskId>/ を作成する
    // その配下に、インポートファイル用サブディレクトリ imports/ およびエクスポートファイル用サブディレクトリ exports/ を作成する
    const shareServiceClient = createShareServiceClient();
    const shareClient = shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
    await shareClient.createIfNotExists();
    const taskDirClient = shareClient.getDirectoryClient(taskId);
    await taskDirClient.createIfNotExists();
    await taskDirClient.getDirectoryClient(AppConstants.AZURE_FILES.IMPORTS_DIRECTORY).createIfNotExists();
    await taskDirClient.getDirectoryClient(AppConstants.AZURE_FILES.EXPORTS_DIRECTORY).createIfNotExists();
    workspaceCreated = true;
    context.log(`[TaskExecuteFunc] Azure Files作業ディレクトリ作成完了: TaskWorkspaces/${taskId}/`);

    // 9. タスク種別 (Task.taskType) が管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) の場合のファイル処理
    if (task.taskType === AppConstants.TaskType.MgmtItemImport) {
      // a. ステップ3.で取得したタスク情報のタスクパラメータparametersJsonから、
      // importedFileBlobPath (Blob上の一時CSVファイルパス、「{licenseId}/imports/{taskId}/assetsfield_def.csv」の形式) を取得する
      const params = JSON.parse(task.parametersJson || "{}");
      const importedFileBlobPath = params.importedFileBlobPath;
      if (!importedFileBlobPath) {
        // 取得した情報が不足/不正の場合：Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、
        // errorCodeをEMET0009に、タスク詳細（resultMessage）を対応するメッセージに更新する
        context.error(`[TaskExecuteFunc] importedFileBlobPathが未設定です`);
        await updateTaskToError(
          taskId,
          AppConstants.ERROR_CODES.EMET0009,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009),
          originalTaskUpdatedAt!
        );
        return;
      }

      // b. importedFileBlobPath と管理項目定義ファイルを保存するAzure Blob Storageのコンテナ名環境変数を合わせて
      // 一時CSVファイルのパスを組み立てて、一時CSVファイルをステップ8.で作成したAzure Files上の
      // TaskWorkspaces/<taskId>/imports/ ディレクトリへ、固定ファイル名 assetsfield_def.csv でダウンロード（コピー）する
      const containerName = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;
      const blobServiceClient = createBlobServiceClient();
      const blobClient = blobServiceClient
        .getContainerClient(containerName)
        .getBlobClient(importedFileBlobPath);
      const fileClient = taskDirClient
        .getDirectoryClient(AppConstants.AZURE_FILES.IMPORTS_DIRECTORY)
        .getFileClient(AppConstants.FILE_NAMES.ASSETSFIELD_DEF_CSV);

      const downloadResponse = await blobClient.downloadToBuffer();
      await fileClient.uploadData(downloadResponse);
      context.log(`[TaskExecuteFunc] 管理項目定義インポートファイル処理完了: ${importedFileBlobPath} -> TaskWorkspaces/${taskId}/imports/assetsfield_def.csv`);
    }
    // 他のタスク種別の場合: ステップ9.はスキップする

    // 10. タスクTaskテーブルから入力タスクのステータスを RUNBOOK_SUBMITTED に、開始日時を現在の日時（UTC協定世界時）に更新する
    // 条件：IDが入力のtaskIdと一致し、最終更新日時がステップ3.で取得した最終更新日時と一致する
    const updateTaskResult = await prisma.task.updateMany({
      where: {
        id: taskId,
        updatedAt: originalTaskUpdatedAt!, // 楽観ロック条件
      },
      data: {
        status: AppConstants.TaskStatus.RunbookSubmitted,
        startedAt: new Date(),
      },
    });

    if (updateTaskResult.count === 0) {
      // 更新した件数が0件の場合（対象タスクに対してユーザーが中止操作を行った）：
      // ステップ8.で作成したAzure Files上の一時作業ディレクトリの削除を行う
      // コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、
      // 使用中のタスクID（currentTaskId） をnull に更新する
      context.warn(`[TaskExecuteFunc] タスクID ${taskId} は他プロセスにより変更・キャンセルされました。補償処理を実行します。`);

      // 補償処理を実行
      await Promise.all([
        // 作業ディレクトリ削除
        (async () => {
          try {
            const shareClient = shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
            const taskDirClient = shareClient.getDirectoryClient(taskId);
            await deleteTaskWorkspaceDirectory(taskDirClient, context);
          } catch (e) {
            logErrorWithStack(context, `[TaskExecuteFunc] 作業ディレクトリ削除失敗:`, e);
          }
        })(),
        // コンテナステータスをIDLEに戻す
        // 条件：対象VM名、対象コンテナ名、ステータス=BUSY、使用中タスクID=現在のタスクIDが一致する場合のみ復旧
        prisma.containerConcurrencyStatus.updateMany({
          where: {
            targetVmName: task.targetVmName,
            targetContainerName: task.targetContainerName,
            status: "BUSY",
            currentTaskId: taskId,
          },
          data: {
            status: "IDLE",
            currentTaskId: null,
          },
        }).catch(e => logErrorWithStack(context, `[TaskExecuteFunc] コンテナステータス復旧失敗:`, e))
      ]);

      return; // 処理終了
    }

    context.log(`[TaskExecuteFunc] タスクステータスをRUNBOOK_SUBMITTEDに更新完了: ${taskId}`);

    // 11. REST API リクエストを構築する
    // タスク種別に応じたRunbook（基盤スクリプト）名を環境変数から取得（事前チェック済み）
    let runbookName: string;
    switch (task.taskType) {
      case AppConstants.TaskType.MgmtItemImport:
        runbookName = process.env.RUNBOOK_MGMT_ITEM_IMPORT!;
        break;
      case AppConstants.TaskType.MgmtItemExport:
        runbookName = process.env.RUNBOOK_MGMT_ITEM_EXPORT!;
        break;
      case AppConstants.TaskType.OpLogExport:
        runbookName = process.env.RUNBOOK_OPLOG_EXPORT!;
        break;
      default:
        // この時点では到達しないはず（事前チェックで処理済み）
        // しかし防御的プログラミングとして適切なエラー処理を行う
        context.error(`[TaskExecuteFunc] 予期しないタスクタイプ: ${task.taskType}`);
        await updateTaskToErrorAfterRunbookSubmitted(
          taskId,
          AppConstants.ERROR_CODES.EMET0009,
          formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009)
        );
        return;
    }

    // Runbookパラメータを構築（タスク種別に応じて異なるパラメータを設定）
    const runbookParams = {
      taskId: taskId,
      targetContainerName: task.targetContainerName,
      ...JSON.parse(task.parametersJson || "{}"),
    };

    context.log(`[TaskExecuteFunc] REST APIリクエスト構築完了: Runbook=${runbookName}, HRWGroup=${task.targetHRWGroupName}`);

    // 12. ステップ11.で準備したリクエストでAzure AutomationのRunbookジョブ作成APIを呼び出す
    await createAutomationJob(
      taskId,
      runbookName,
      runbookParams,
      task.targetHRWGroupName
    );
    context.log(`[TaskExecuteFunc] Azure Automation Runbookジョブ作成完了: JobName=${taskId}`);

    // 13. ステップ12.までの処理が正常に完了した場合、以下のタスクレコード保持件数クリーンアップ処理を実行し、
    // 例外が発生した場合でも、catchして適切にログ記録するだけで特に対処せず、1.-12.の処理結果に影響を与えない形で行う
    try {
      await cleanupOldTasks(task.targetServerId, context);
      context.log(`[TaskExecuteFunc] タスクレコード保持件数クリーンアップ処理完了: ${taskId}`);
    } catch (cleanupError) {
      // このクリーンアップ処理中のエラーはcatchした後ログに記録し、特に対処を行わない
      logErrorWithStack(context, `[TaskExecuteFunc] タスクレコード保持件数クリーンアップ処理でエラー発生（処理継続）:`, cleanupError);
    }

    // 14. 正常終了ログを記録し、処理を終了する（メッセージACK）
    context.log(`[TaskExecuteFunc] タスクID ${taskId} の処理が正常に完了しました。`);
  } catch (error) {
    // エラー処理メカニズム：補償処理とタスク状態更新
    logErrorWithStack(context, `[TaskExecuteFunc] タスクID ${taskId} の処理中にエラー発生:`, error);

    // 補償処理（対象コンテナのステータスがBUSYに更新、作業ディレクトリ作成、Runbookジョブ作成された場合の補償）
    const compensationActions: Promise<any>[] = [];

    // コンテナのステータスをBUSYに更新した場合はIDLEに更新する（戻す）
    // ステップ7でコンテナロックが成功した場合、補償処理でIDLEに戻す
    if (containerStatus && task?.targetVmName && task?.targetContainerName) {
      compensationActions.push(
        prisma.containerConcurrencyStatus.updateMany({
          where: {
            targetVmName: task.targetVmName,
            targetContainerName: task.targetContainerName,
            status: "BUSY",
            currentTaskId: taskId,
          },
          data: {
            status: "IDLE",
            currentTaskId: null,
          },
        }).catch((e) =>
          logErrorWithStack(context, `[TaskExecuteFunc] 補償処理: コンテナステータス復旧失敗: タスクID ${taskId}:`, e)
        )
      );
    }

    // Azure Files上の一時作業ディレクトリが作成された場合は一時作業ディレクトリを削除する
    if (workspaceCreated && taskId) {
      compensationActions.push(
        (async () => {
          try {
            const shareServiceClient = createShareServiceClient();
            const shareClient = shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
            const taskDirClient = shareClient.getDirectoryClient(taskId);
            await deleteTaskWorkspaceDirectory(taskDirClient, context);
            context.log(`[TaskExecuteFunc] 補償処理: 作業ディレクトリ削除完了: TaskWorkspaces/${taskId}/`);
          } catch (e) {
            logErrorWithStack(context, `[TaskExecuteFunc] 補償処理: 作業ディレクトリ削除失敗: タスクID ${taskId}:`, e);
          }
        })()
      );
    }

    // 全ての補償処理を並行実行
    await Promise.all(compensationActions);

    // エラー種別ごとに適切なエラーコード・メッセージでタスクを更新
    if (!taskId || !originalTaskUpdatedAt) {
      // taskIdまたはoriginalTaskUpdatedAtが取得できていない場合は、タスク更新をスキップ
      context.error(`[TaskExecuteFunc] タスクID または更新日時が不正のため、タスクステータス更新をスキップします`);
      return;
    }

    // エラーシナリオ別の処理
    if (error instanceof Error && isPrismaError(error)) {
      // DB更新失敗
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0007,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0007),
        originalTaskUpdatedAt,
        error
      );
    } else if (error instanceof Error && isAzureFilesError(error)) {
      // Azure Files作業ディレクトリ作成失敗
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0002,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0002),
        originalTaskUpdatedAt,
        error
      );
    } else if (error instanceof Error && isAzureBlobError(error)) {
      // (管理項目定義インポートタスクの場合) Blobからの一時CSVファイルダウンロード失敗
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0003,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0003),
        originalTaskUpdatedAt,
        error
      );
    } else if (
      error instanceof Error &&
      (error.message.includes("fetch") || error.message.includes("Automation") || error.message.includes("Runbook"))
    ) {
      // Azure Automation Runbookジョブ作成API の呼び出し失敗
      // この時点でタスクステータスは既にRUNBOOK_SUBMITTEDに更新されているため、専用の更新関数を使用
      await updateTaskToErrorAfterRunbookSubmitted(
        taskId,
        AppConstants.ERROR_CODES.EMET0013,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0013),
        error
      );
    } else {
      // 予期せぬ内部エラー
      await updateTaskToError(
        taskId,
        AppConstants.ERROR_CODES.EMET0008,
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0008),
        originalTaskUpdatedAt,
        error instanceof Error ? error : undefined
      );
    }
  }
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("TaskExecuteFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_TASK_INPUT_QUEUE_NAME%",
  handler: TaskExecuteFunc,
});
