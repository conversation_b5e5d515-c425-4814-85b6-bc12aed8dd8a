/**
 * @fileoverview Runbookジョブ監視関数 (RunbookMonitorFunc)
 * @description
 * Azure Functionのタイマートリガーによって定期的に実行され、RUNBOOK_SUBMITTEDステータスの
 * タスクに対応するAzure Automation Runbookジョブの実行状況を監視する関数。
 * ジョブの完了・失敗・タイムアウトを検出し、後続処理のためにRunbookStatusQueueへメッセージを送信する。
 *
 * @trigger Azure Functions タイマートリガー（環境変数 RUNBOOK_MONITOR_INTERVAL_SECONDS で設定）
 * @input データベース (Taskテーブル): status = RUNBOOK_SUBMITTED のすべてのタスクレコード
 * @output Azure Service Bus (RunbookStatusQueue) へのメッセージ送信、Taskテーブルのレコード更新
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext, Timer } from "@azure/functions";
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { createServiceBusClient, getAutomationJobStatus } from "../lib/azureClients";
import { logErrorWithStack } from "../lib/utils";

// 環境変数の取得と初期化
const monitorInterval = parseInt(process.env.RUNBOOK_MONITOR_INTERVAL_SECONDS || "30", 10);
const runbookTimeout = parseInt(process.env.RUNBOOK_TIMEOUT_SECONDS || String(5 * 3600), 10); // デフォルト5時間
const automationAccount = process.env.AZURE_AUTOMATION_ACCOUNT_NAME!;
const subscriptionId = process.env.SUBSCRIPTION_ID!;
const resourceGroupName = process.env.RESOURCE_GROUP_NAME!;
const runbookStatusQueueName = process.env.SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME!;

if (!automationAccount || !subscriptionId || !resourceGroupName || !runbookStatusQueueName) {
  throw new Error("RunbookMonitorFuncに必要な環境変数が不足しています");
}

// Service Bus クライアント（関数実行時に作成）
let serviceBusClient: any = null;
let sender: any = null;

// 終了・実行中ステータス定義
const END_STATUSES = [
  "Completed", "Failed", "Removing", "Resuming", "Stopped", "Stopping", "Suspended", "Suspending"
];
const ACTIVE_STATUSES = [
  "New", "Activating", "Running", "Blocked", "Disconnected"
];

/**
 * RunbookMonitorFunc - Runbookジョブ監視及び結果ポーリング関数
 *
 * 処理ステップ:
 * 1. Runbookジョブ監視関数がタイマートリガーにより起動される。処理開始のログを記録する。
 * 2. データベースの Task テーブルから、status が RUNBOOK_SUBMITTED である全てのタスクレコードを取得する。
 * 3. 取得した各タスクレコードに対して、4.-9.の処理をループで行う。各タスクの処理は独立している。
 * 4. タイムアウトチェック: startedAtから現在の日時までの時間を計算し、RUNBOOK_TIMEOUT_SECONDSと比較
 * 5. (タイムアウトしていない場合)ジョブステータスポーリング: Azure Automationのジョブ取得APIを呼び出し
 * 6. (タイムアウトしていない場合)取得したジョブステータスproperties.statusを判定
 * 7. Taskテーブルから当該タスクのステータスをRUNBOOK_PROCESSINGに更新（楽観ロック制御）
 * 8. Runbookジョブのステータスを通知するメッセージを構築し、Service Bus (RunbookStatusQueue) へ送信
 * 9. 次のタスクレコードの判定に入る
 * 10. RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して終了
 *
 * エラー処理:
 * 各エラーシナリオでエラーログ記録し、当該タスクは次回のRunbookジョブ監視関数に持ち越す。
 */
export async function RunbookMonitorFunc(_timer: Timer, context: InvocationContext) {
  try {
    // Service Busクライアントを初期化（並行実行時の安全性確保）
    serviceBusClient = createServiceBusClient();
    sender = serviceBusClient.createSender(runbookStatusQueueName);

    // 1. Runbookジョブ監視関数がタイマートリガーにより起動される。処理開始のログを記録する。
    context.log("[RunbookMonitorFunc] Runbookジョブ監視関数開始");

    // 2. データベースの Task テーブルから、status が RUNBOOK_SUBMITTED である全てのタスクレコードを取得する。
    const tasks = await prisma.task.findMany({
      where: { status: AppConstants.TaskStatus.RunbookSubmitted },
    });

    // 取得失敗または対象タスクが存在しない場合、ログに記録し処理を終了する。
    if (!tasks.length) {
      context.log("[RunbookMonitorFunc] RUNBOOK_SUBMITTEDステータスのタスクが存在しません。処理を終了します。");
      return;
    }

    context.log(`[RunbookMonitorFunc] RUNBOOK_SUBMITTEDステータスのタスク数: ${tasks.length}`);

    // 3. 取得した各タスクレコードに対して、4.-9.の処理をループで行う。各タスクの処理は独立しており、
    // あるタスクの処理失敗が他のタスクの処理を妨げない。
    for (const task of tasks) {
      try {
        // startedAt未設定タスクはスキップ（安全性チェック）
        if (!task.startedAt) {
          context.warn(`[RunbookMonitorFunc] タスク${task.id}はstartedAt未設定のためスキップ`);
          continue;
        }

        // 4. タイムアウトチェック:
        // タスクの開始日時startedAtから現在の日時までの時間を計算（両方UTC協定世界時で計算）し、
        // 環境変数 RUNBOOK_TIMEOUT_SECONDS で定義されたRunbookジョブのタイムアウト時間と比較して、
        // 超過しているか（タイムアウトしたか）判断する。
        const now = new Date();
        const startedAt = new Date(task.startedAt!);
        const elapsed = (now.getTime() - startedAt.getTime()) / 1000;

        let automationJobStatus = "";
        let exception = "";
        let shouldProcess = false;

        if (elapsed > runbookTimeout) {
          // タイムアウトした場合
          automationJobStatus = "Timeout";
          shouldProcess = true;
          context.log(`[RunbookMonitorFunc] タスク${task.id}はタイムアウトしました (${elapsed}s > ${runbookTimeout}s)`);
        } else {
          // 5. (タイムアウトしていない場合)ジョブステータスポーリング :
          // taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの現在のステータスと
          // （例外が発生した場合）例外情報を問い合わせる。
          const jobName = task.id; // taskId=jobNameの前提

          try {
            const jobResult = await getAutomationJobStatus(jobName);
            if (!jobResult.exists) {
              // ジョブが存在しない場合（404）- 通常は発生しないが、ログ記録してスキップ
              context.warn(`[RunbookMonitorFunc] タスク${task.id}のジョブが存在しません（404）`);
              continue;
            }
            const jobStatus = jobResult.jobData?.properties?.status || "";
            exception = jobResult.jobData?.properties?.exception || "";

            context.log(`[RunbookMonitorFunc] タスク${task.id}のジョブステータス取得成功: ${jobStatus}`);

            // 6. (タイムアウトしていない場合)5.で取得したジョブステータスproperties.statusを判定する :
            if (ACTIVE_STATUSES.includes(jobStatus)) {
              // 準備中/実行中/一時状態（New, Activating, Running, Blocked, Disconnectedのいずれか）の場合：
              // 当該タスクが処理対象外のため、ステップ3.に戻って、次のタスクレコードの判定に入る。
              context.log(`[RunbookMonitorFunc] タスク${task.id}は実行中状態のため処理対象外: ${jobStatus}`);
              continue;
            } else if (END_STATUSES.includes(jobStatus)) {
              // 特定の終了状態/異常状態（Completed, Failed, Removing, Resuming, Stopped, Stopping, Suspended, Suspendingのいずれか）の場合：
              // ステップ7.の処理に進める。
              automationJobStatus = jobStatus;
              shouldProcess = true;
              context.log(`[RunbookMonitorFunc] タスク${task.id}は終了状態のため処理対象: ${jobStatus}`);
            } else {
              // 未知のステータス
              context.warn(`[RunbookMonitorFunc] タスク${task.id}の未知ジョブステータス: ${jobStatus}`);
              continue;
            }
          } catch (err: any) {
            // API の呼び出しに失敗した場合（レスポンスのHTTPステータスコードが200でない場合）：
            // エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
            // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
            logErrorWithStack(context, `[RunbookMonitorFunc] タスク${task.id}のAzure Automation API呼び出し失敗:`, err);
            continue;
          }
        }

        if (!shouldProcess) continue;

        // 7. Taskテーブルから当該タスクのステータスをRUNBOOK_PROCESSINGに更新する。
        // 条件：IDがステップ2.で取得した各タスクレコードのtaskIdと一致し、
        // 最終更新日時がステップ2.で取得した最終更新日時と一致する。
        try {
          const updateResult = await prisma.task.updateMany({
            where: {
              id: task.id,
              updatedAt: task.updatedAt, // 楽観ロック制御
            },
            data: { status: AppConstants.TaskStatus.RunbookProcessing },
          });

          // DB更新失敗、または更新した件数が0件の場合：
          // エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
          if (updateResult.count === 0) {
            context.error(`[RunbookMonitorFunc] タスク${task.id}の楽観ロック失敗：他プロセスによりタスクが変更されました`);
            continue;
          }

          context.log(`[RunbookMonitorFunc] タスク${task.id}のステータス更新完了: RUNBOOK_PROCESSING`);
        } catch (err: any) {
          // DB更新失敗の場合：エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
          logErrorWithStack(context, `[RunbookMonitorFunc] タスク${task.id}のDB更新失敗:`, err);
          continue;
        }

        // 8. Runbookジョブのステータスを通知するメッセージを構築し、Service Bus (RunbookStatusQueue) へ送信:
        // メッセージにはtaskId（当該タスクのID）、automationJobStatus（タイムアウトした場合は"Timeout"/
        // タイムアウトしていない場合はステップ5.で取得した実際のジョブステータスproperties.status）、
        // exception（ステップ5.で取得した例外情報properties.exception）が含まれる。
        // 送信失敗の場合は処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新（戻す）する。
        try {
          const message = {
            body: {
              taskId: task.id,
              automationJobStatus,
              exception,
            },
          };

          await sender.sendMessages(message);
          context.log(`[RunbookMonitorFunc] タスク${task.id}のRunbookStatusQueueへメッセージ送信完了: ${automationJobStatus}`);
        } catch (err: any) {
          // 送信失敗の場合：
          // ・処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新する（戻す）。
          //   条件：IDが処理対象タスクのタスクIDと一致し、ステータスがRUNBOOK_PROCESSINGと一致する。
          // ・エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          //   当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
          try {
            const rollbackResult = await prisma.task.updateMany({
              where: {
                id: task.id,
                status: AppConstants.TaskStatus.RunbookProcessing, // ステータス条件による制御
              },
              data: { status: AppConstants.TaskStatus.RunbookSubmitted },
            });

            if (rollbackResult.count === 0) {
              context.error(`[RunbookMonitorFunc] タスク${task.id}のステータス戻し失敗：タスクが既に他の状態に変更されています`);
            } else {
              context.log(`[RunbookMonitorFunc] タスク${task.id}のステータスをRUNBOOK_SUBMITTEDに戻しました`);
            }
          } catch (rollbackErr: any) {
            logErrorWithStack(context, `[RunbookMonitorFunc] タスク${task.id}のステータス戻し失敗:`, rollbackErr);
          }

          context.error(`[RunbookMonitorFunc] タスク${task.id}のRunbookStatusQueue送信失敗:`, err);
          continue;
        }
      } catch (err: any) {
        // 予期せぬ内部エラー: エラーログ記録。ループ内ならループ継続し、
        // 当該タスクは次回のRunbookジョブ監視関数に持ち越す。
        logErrorWithStack(context, `[RunbookMonitorFunc] タスク${task.id}処理中の予期せぬ内部エラー:`, err);
        continue;
      }
      // 9. ステップ3.に戻って、次のタスクレコードの判定に入る。
    }

    // 10. RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して、
    // Runbookジョブ監視関数を終了する。
    context.log("[RunbookMonitorFunc] RUNBOOK_SUBMITTEDタスクの監視処理が正常に完了しました");
  } catch (err: any) {
    // Taskテーブルからのデータ取得失敗: エラーログ記録。処理終了。
    logErrorWithStack(context, `[RunbookMonitorFunc] Taskテーブルからのデータ取得に失敗しました。`, err);
  }
}

// Azure Functionsタイマートリガー登録
app.timer("RunbookMonitorFunc", {
  schedule: `*/${monitorInterval} * * * * *`, // 秒単位の定期実行
  handler: RunbookMonitorFunc,
}); 